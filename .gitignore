# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Environment variables
.env
.env.local
.env.production
.env.staging

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Project specific
output/
*.pdf
*.png
*.jpg
*.jpeg

# Data files (too large for git)
../optionhistory/
../optionshistory/
optionshistory/
data/
*.csv

# Logs
*.log
logs/

# Cache
.cache/
*.cache

# Jupyter Notebooks
.ipynb_checkpoints/

# pytest
.pytest_cache/

# Coverage
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

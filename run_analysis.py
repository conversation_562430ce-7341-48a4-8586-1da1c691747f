#!/usr/bin/env python3
"""
Simple runner script for Hidden Outliers Analysis
"""

import sys
import os
import argparse
import glob
import re
from datetime import datetime

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from main import HiddenOutliersAnalyzer
from config import list_supported_tickers

def find_most_recent_optionshistory_data():
    """
    Find the most recent SPX data file from optionshistory directory.
    Expected pattern: optionshistory/{year}_{quarter}_option_chain/spx_complete_{year}_{quarter}.csv
    """
    optionshistory_dir = "optionshistory"

    if not os.path.exists(optionshistory_dir):
        return None

    # Pattern to match year_quarter_option_chain directories
    pattern = re.compile(r'^(\d{4})_(q[1-4])_option_chain$')

    found_files = []

    # Look for directories matching the pattern
    for item in os.listdir(optionshistory_dir):
        item_path = os.path.join(optionshistory_dir, item)
        if os.path.isdir(item_path):
            match = pattern.match(item)
            if match:
                year, quarter = match.groups()
                # Look for the corresponding CSV file
                csv_file = os.path.join(item_path, f"spx_complete_{year}_{quarter}.csv")
                if os.path.exists(csv_file):
                    # Convert quarter to number for sorting
                    quarter_num = int(quarter[1])  # Extract number from 'q1', 'q2', etc.
                    found_files.append((int(year), quarter_num, csv_file))

    if not found_files:
        return None

    # Sort by year and quarter (most recent first)
    found_files.sort(key=lambda x: (x[0], x[1]), reverse=True)

    return found_files[0][2]  # Return the path of the most recent file

def main():
    parser = argparse.ArgumentParser(description='Generate Hidden Outliers Analysis PDF')
    parser.add_argument('--data', '-d', type=str, help='Path to SPX options data CSV file')
    parser.add_argument('--output', '-o', type=str, default='output', help='Output directory')
    parser.add_argument('--auto-find', action='store_true',
                       help='Automatically find the most recent data file from optionshistory directory')
    parser.add_argument('--no-ai', action='store_true',
                       help='Disable AI-generated narratives (use default text)')
    parser.add_argument('--ticker', type=str, default='SPX',
                       help=f'Ticker symbol to analyze (supported: {", ".join(list_supported_tickers())})')

    args = parser.parse_args()

    # Determine data file to use
    data_file = None
    data_source_description = "Synthetic data"

    if args.auto_find:
        # Try to find the most recent file from optionshistory
        auto_found_file = find_most_recent_optionshistory_data()
        if auto_found_file:
            data_file = auto_found_file
            data_source_description = f"Auto-found: {auto_found_file}"
        else:
            print("Warning: No data files found in optionshistory directory. Using synthetic data.")
    elif args.data:
        # Use explicitly provided data file
        if os.path.exists(args.data):
            data_file = args.data
            data_source_description = f"Provided: {args.data}"
        elif os.path.exists(os.path.join('data', args.data)):
            data_file = os.path.join('data', args.data)
            data_source_description = f"Provided: {data_file}"
        else:
            print(f"Warning: Data file {args.data} not found. Using synthetic data.")
    else:
        # No explicit file provided, try to auto-find as fallback
        auto_found_file = find_most_recent_optionshistory_data()
        if auto_found_file:
            data_file = auto_found_file
            data_source_description = f"Auto-found: {auto_found_file}"

    print("="*60)
    print("HIDDEN OUTLIERS ANALYSIS GENERATOR")
    print("="*60)
    print(f"Ticker: {args.ticker}")
    print(f"Data source: {data_source_description}")
    print(f"Output directory: {args.output}")
    print("="*60)

    analyzer = HiddenOutliersAnalyzer(
        ticker=args.ticker,
        output_dir=args.output,
        use_ai_narrative=not args.no_ai
    )
    result = analyzer.run_full_analysis(data_file=data_file, auto_find=args.auto_find)

    if result['success']:
        print(f"\n{'='*60}")
        print("ANALYSIS COMPLETE!")
        print(f"{'='*60}")
        print(f"📊 PDF Report: {result['pdf_path']}")
        print(f"📈 Detailed Chart: {result['detailed_chart_path']}")
        print(f"📊 Vanna Exposure Chart: {result['vanna_chart_path']}")
        print(f"📈 Summary Chart: {result['summary_chart_path']}")
        print(f"{'='*60}")

        # Print summary
        metrics = result['metrics']
        print("\n📋 ANALYSIS SUMMARY:")
        print(f"   • Total Records: {metrics['total_rows']:,}")
        print(f"   • Trading Days: {metrics['trading_days']}")
        print(f"   • Current {args.ticker}: ${metrics['current_price']:.2f}")
        print(f"   • Market Regime: {metrics['market_regime'].replace('_', ' ').title()}")
        print(f"   • Anomalies Detected: {metrics['anomalies_count']}")

        print("\nNext steps:")
        print("1. Review the PDF report for detailed analysis")
        print("2. Examine the charts for visual patterns")
        print("3. Monitor market conditions for trigger events")
        print("="*60)
    else:
        print(f"❌ Analysis failed: {result.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()


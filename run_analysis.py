#!/usr/bin/env python3
"""
Simple runner script for Hidden Outliers Analysis
"""

import sys
import os
import argparse

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from hidden_outliers_generator import HiddenOutliersAnalyzer

def main():
    parser = argparse.ArgumentParser(description='Generate Hidden Outliers Analysis PDF')
    parser.add_argument('--data', '-d', type=str, help='Path to SPX options data CSV file')
    parser.add_argument('--output', '-o', type=str, default='output', help='Output directory')
    
    args = parser.parse_args()
    
    # Use data file if provided, otherwise None (will use synthetic data)
    data_file = None
    if args.data:
        if os.path.exists(args.data):
            data_file = args.data
        elif os.path.exists(os.path.join('data', args.data)):
            data_file = os.path.join('data', args.data)
        else:
            print(f"Warning: Data file {args.data} not found. Using synthetic data.")
    
    print("="*60)
    print("HIDDEN OUTLIERS ANALYSIS GENERATOR")
    print("="*60)
    print(f"Data source: {'Real data' if data_file else 'Synthetic data'}")
    print(f"Output directory: {args.output}")
    print("="*60)
    
    analyzer = HiddenOutliersAnalyzer(data_file=data_file, output_dir=args.output)
    pdf_path, chart_path = analyzer.run_analysis()
    
    print(f"\n{'='*60}")
    print("ANALYSIS COMPLETE!")
    print(f"{'='*60}")
    print(f"📊 PDF Report: {pdf_path}")
    print(f"📈 Charts: {chart_path}")
    print(f"{'='*60}")
    print("\nNext steps:")
    print("1. Review the PDF report for detailed analysis")
    print("2. Examine the charts for visual patterns")
    print("3. Monitor market conditions for trigger events")
    print("="*60)

if __name__ == "__main__":
    main()


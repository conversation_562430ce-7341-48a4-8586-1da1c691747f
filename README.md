# Hidden Outliers Analysis Generator

This project generates comprehensive PDF analysis of options flow anomalies with forward-looking perspective, specifically focusing on the "Hidden Outliers" patterns that reveal true price trajectory risks.

## Overview

The Hidden Outliers analysis identifies four critical options flow anomalies:

1. **Gamma Exposure (GEX) Danger Zone** - Low GEX levels indicate no dealer hedging buffer
2. **Daily Theta Burn Pressure** - Constant selling pressure from time decay
3. **Put Charm Gravitational Pull** - Downward pressure toward put strike levels
4. **Vomma Volatility Feedback Loop** - Self-reinforcing volatility mechanism

When these converge, they create a "trap-freeze-coil" pattern that typically precedes 2-4% moves within 3-5 trading days.

## Key Features

- **Ticker-Agnostic Architecture**: Supports SPX, VIX, TLT, NDX and other major indices
- **Forward-Looking Analysis**: Focuses on 0-30 DTE options for future price projections
- **AI-Powered Narratives**: OpenAI integration for intelligent market commentary
- **Comprehensive Charts**: Vanna exposure, charm analysis, and forward-looking metrics
- **Professional PDF Reports**: Publication-ready analysis documents

## Project Structure

```
hidden_outliers_project/
├── src/
│   ├── main.py                 # Main application orchestrator
│   ├── analytics.py            # Options metrics and calculations
│   ├── visualization.py        # Chart generation
│   ├── report_generator.py     # PDF report creation
│   ├── data_manager.py         # Data loading and management
│   └── config.py              # Ticker configurations
├── data/
│   └── spx_complete_2025_q2.csv    # Sample SPX options data
├── output/                         # Generated reports and charts
├── requirements.txt                # Python dependencies
├── README.md                      # This file
└── run_analysis.py               # Simple runner script
```

## Installation

1. Install Python 3.7 or higher
2. Install required packages:
   ```bash
   pip install -r requirements.txt
   ```
3. Set up your OpenAI API key (optional, for AI-generated narratives):
   ```bash
   cp .env.example .env
   # Edit .env and add your OpenAI API key
   ```

## Usage

### Basic Usage (with synthetic data)
```bash
python run_analysis.py
```

### Auto-find most recent data from optionshistory
```bash
python run_analysis.py --auto-find
```

### With your own data file
```bash
python run_analysis.py --data data/spx_complete_2025_q2.csv
```

### Analyze different tickers
```bash
python run_analysis.py --ticker NDX --auto-find
python run_analysis.py --ticker VIX --auto-find
python run_analysis.py --ticker TLT --auto-find
```

### Advanced Usage
```bash
python src/main.py --ticker SPX --data data/your_data.csv --output-dir custom_output
```

## Command Line Options

- `--data, -d`: Path to SPX options data CSV file (optional, uses synthetic data if not provided)
- `--output, -o`: Output directory for reports and charts (default: 'output')
- `--auto-find`: Automatically find the most recent data file from optionshistory directory
- `--no-ai`: Disable AI-generated narratives and use default text instead

## Optionshistory Directory Structure

The script can automatically find the most recent SPX options data from an `optionshistory` directory with this structure:

```
optionshistory/
├── 2024_q3_option_chain/
│   └── spx_complete_2024_q3.csv
├── 2024_q4_option_chain/
│   └── spx_complete_2024_q4.csv
├── 2025_q1_option_chain/
│   └── spx_complete_2025_q1.csv
└── 2025_q2_option_chain/
    └── spx_complete_2025_q2.csv
```

When using `--auto-find` or when no data file is specified, the script will automatically select the most recent file based on year and quarter.

## AI-Generated Narratives

The system can generate professional, AI-powered narratives using OpenAI's GPT-4 model:

- **Executive Summary**: AI analyzes your data and generates a compelling executive summary
- **Detailed Analysis**: Technical deep-dive into the options flow mechanics and risk factors
- **Contextual Insights**: AI provides market context and actionable trading insights

To enable AI narratives:
1. Get an OpenAI API key from [OpenAI Platform](https://platform.openai.com/)
2. Copy `.env.example` to `.env` and add your API key
3. Run the analysis normally - AI narratives are enabled by default

Use `--no-ai` flag to disable AI and use default narratives.

## Data Format

If providing your own data, the CSV file should contain these columns:
- `date`: Trading date
- `Strike`: Option strike price
- `Expiry Date`: Option expiration date
- `Call/Put`: 'c' for calls, 'p' for puts
- `Delta`, `Gamma`, `Vega`, `Theta`: Option Greeks
- `Open Interest`: Number of open contracts
- `spx_close`: SPX closing price

## Output

The program generates:
1. **PDF Report**: Comprehensive analysis with charts and detailed findings
2. **Chart Images**: High-resolution charts showing the analysis
3. **Data Files**: Calculated metrics in CSV format

## Key Features

- **AI-Powered Narratives**: GPT-4 generated executive summaries and detailed analysis tailored to your data
- **Professional PDF Reports**: Multi-page analysis with executive summary, detailed findings, and risk assessment
- **Enhanced Visualizations**: Dark-themed charts with dramatic annotations matching the "Hidden Outliers" theme
- **Auto-Discovery**: Automatically finds the most recent data from optionshistory directory structure
- **Flexible Data Input**: Works with real data or generates realistic synthetic scenarios
- **Command Line Interface**: Easy to use with various options
- **Comprehensive Analysis**: Calculates all key options flow metrics

## Risk Assessment Levels

- **CRITICAL**: GEX near zero, no hedging buffer
- **HIGH**: Significant theta burn or charm buildup
- **EXTREME**: Vomma feedback loops active
- **ELEVATED**: Put/call ratios showing bearish bias

## Author

Generated by Manus AI - Advanced Options Flow Analysis System

## License

This project is provided as-is for educational and analysis purposes.


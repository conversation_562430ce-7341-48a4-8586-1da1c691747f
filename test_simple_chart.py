#!/usr/bin/env python3
"""
Test simplified version of professional chart to isolate the readability issue.
"""

import matplotlib.pyplot as plt
import numpy as np

def test_simple_professional_chart():
    """Create a simplified version of the professional chart."""
    
    # NDX values
    current_price = 22679.01
    spx_base = 6181
    scale_factor = current_price / spx_base
    
    # Calculate chart ranges
    flip_point = current_price + (4 * scale_factor)
    bomb_point = current_price + (19 * scale_factor)
    chart_min = current_price - (31 * scale_factor)
    chart_max = current_price + (49 * scale_factor)
    
    print(f"Creating simplified chart with range: {chart_min:.2f} to {chart_max:.2f}")
    
    # Create figure with simple layout
    fig = plt.figure(figsize=(12, 8), facecolor='white', dpi=100)
    
    # Simple 2x1 layout: title + main chart
    gs = fig.add_gridspec(2, 1, height_ratios=[0.2, 0.8], hspace=0.3)
    
    # Title
    title_ax = fig.add_subplot(gs[0, :])
    title_ax.set_xlim(0, 1)
    title_ax.set_ylim(0, 1)
    title_ax.axis('off')
    title_ax.text(0.5, 0.5, f'NDX Charm Bomb Test - Current: {current_price:.0f}',
                  ha='center', va='center', fontsize=16, weight='bold')
    
    # Main chart
    main_ax = fig.add_subplot(gs[1, :])
    
    # Generate test data
    strikes = np.linspace(chart_min, chart_max, 200)
    
    # Simple charm-like curve
    charm_data = np.where(strikes <= current_price,
                         3200 * np.exp(-((strikes - chart_min) / (12 * scale_factor))**1.5) + 600,
                         600 * np.exp(-((strikes - current_price) / (8 * scale_factor))**2))
    
    # Plot
    main_ax.plot(strikes, charm_data, 'teal', linewidth=3, label='Charm')
    main_ax.axvline(x=current_price, color='red', linestyle='--', linewidth=2, label=f'Current: {current_price:.0f}')
    main_ax.axvline(x=flip_point, color='orange', linestyle='--', linewidth=2, label=f'Flip: {flip_point:.0f}')
    main_ax.axvline(x=bomb_point, color='purple', linestyle='--', linewidth=2, label=f'Bomb: {bomb_point:.0f}')
    
    # Add some text annotations
    main_ax.text(bomb_point + 5*scale_factor, 2000, 'BOMB ZONE', 
                fontsize=12, color='red', weight='bold', ha='center')
    
    # Set limits
    main_ax.set_xlim(chart_min, chart_max)
    main_ax.set_ylim(0, 4000)
    
    # Labels and formatting
    main_ax.set_xlabel('Strike Price', fontsize=12)
    main_ax.set_ylabel('Charm Value', fontsize=12)
    main_ax.legend()
    main_ax.grid(True, alpha=0.3)
    
    # Save
    plt.tight_layout()
    plt.savefig('test_simple_professional.png', dpi=100, bbox_inches='tight', 
                facecolor='white', edgecolor='none')
    print(f"Simple professional chart saved as: test_simple_professional.png")
    
    plt.close()

if __name__ == "__main__":
    test_simple_professional_chart()

#!/usr/bin/env python3
"""
Test script to demonstrate AI integration functionality
"""

import os
import sys
from dotenv import load_dotenv

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_env_setup():
    """Test if environment is properly set up"""
    print("Testing environment setup...")
    
    # Load environment variables
    load_dotenv()
    
    # Check for OpenAI API key
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key:
        # Mask the key for security
        masked_key = api_key[:8] + "..." + api_key[-4:] if len(api_key) > 12 else "***"
        print(f"✅ OpenAI API key found: {masked_key}")
        return True
    else:
        print("❌ No OpenAI API key found in .env file")
        print("To enable AI features:")
        print("1. Copy .env.example to .env")
        print("2. Add your OpenAI API key to the .env file")
        print("3. Get an API key from: https://platform.openai.com/")
        return False

def test_ai_analyzer():
    """Test the AI analyzer functionality"""
    print("\nTesting AI analyzer...")
    
    try:
        from hidden_outliers_generator import HiddenOutliersAnalyzer
        
        # Test with AI enabled
        analyzer_ai = HiddenOutliersAnalyzer(use_ai_narrative=True)
        print(f"✅ AI analyzer created: AI enabled = {analyzer_ai.use_ai_narrative}")
        print(f"✅ OpenAI client available = {analyzer_ai.openai_client is not None}")
        
        # Test with AI disabled
        analyzer_no_ai = HiddenOutliersAnalyzer(use_ai_narrative=False)
        print(f"✅ Non-AI analyzer created: AI enabled = {analyzer_no_ai.use_ai_narrative}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Error importing analyzer: {e}")
        return False
    except Exception as e:
        print(f"❌ Error creating analyzer: {e}")
        return False

def test_narrative_generation():
    """Test narrative generation (without making API calls)"""
    print("\nTesting narrative generation methods...")
    
    try:
        from hidden_outliers_generator import HiddenOutliersAnalyzer
        import pandas as pd
        
        # Create analyzer
        analyzer = HiddenOutliersAnalyzer(use_ai_narrative=True)
        
        # Test default narrative
        default_narrative = analyzer.get_default_narrative()
        print(f"✅ Default narrative generated ({len(default_narrative)} characters)")
        
        # Test default detailed analysis
        default_analysis = analyzer.get_default_detailed_analysis()
        print(f"✅ Default detailed analysis generated ({len(default_analysis)} characters)")
        
        # Create sample metrics for testing
        sample_data = {
            'date': [pd.Timestamp('2025-06-26')],
            'gex': [75.5],
            'theta': [-35.2],
            'charm': [-1200.0],
            'vomma': [180.5],
            'put_oi': [150000],
            'call_oi': [100000],
            'spx_close': [6025]
        }
        metrics_df = pd.DataFrame(sample_data)
        
        # Test trend analysis
        trends = analyzer.analyze_trends(metrics_df)
        print(f"✅ Trend analysis: {trends}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing narrative generation: {e}")
        return False

def main():
    """Run all tests"""
    print("="*60)
    print("AI INTEGRATION TEST SUITE")
    print("="*60)
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Environment setup
    if test_env_setup():
        tests_passed += 1
    
    # Test 2: AI analyzer creation
    if test_ai_analyzer():
        tests_passed += 1
    
    # Test 3: Narrative generation
    if test_narrative_generation():
        tests_passed += 1
    
    print(f"\n{'='*60}")
    print(f"TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    print(f"{'='*60}")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! AI integration is ready to use.")
        print("\nTo run with AI narratives:")
        print("python run_analysis.py --auto-find")
        print("\nTo run without AI:")
        print("python run_analysis.py --auto-find --no-ai")
    else:
        print("⚠️  Some tests failed. Check the output above for details.")

if __name__ == "__main__":
    main()

"""
Advanced Chart Generator Module

This module provides functionality to generate the three main options analytics charts:
1. Charm Profile: The Time Decay Battlefield
2. Gamma-Charm Interaction scatter plot
3. Vanna-Charm Double Trap chart

Integrated into the Hidden Outliers Analysis system.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import seaborn as sns
from matplotlib.patches import Rectangle
import warnings
warnings.filterwarnings('ignore')

# Set style
plt.style.use('default')
sns.set_palette("husl")


class ChartGenerator:
    """
    A class to generate options analytics charts.
    
    This class provides methods to create professional-looking charts
    that match the style and content of the original analytics charts.
    """
    
    def __init__(self, figsize: Tuple[int, int] = (14, 10)):
        """
        Initialize the chart generator.
        
        Args:
            figsize (Tuple[int, int]): Default figure size
        """
        self.figsize = figsize
        self.colors = {
            'positive_charm': '#2E8B57',  # <PERSON> green
            'negative_charm': '#DC143C',  # Crimson
            'neutral': '#4682B4',         # Steel blue
            'background_positive': '#E6F3E6',  # Light green
            'background_negative': '#FFE6E6',  # Light red
            'critical_line': '#FF8C00',   # Dark orange
            'text_critical': '#B22222'    # Fire brick
        }
    
    def create_charm_profile_chart(self, charm_data: pd.DataFrame, 
                                 current_price: float,
                                 charm_levels: Dict[str, float],
                                 save_path: Optional[str] = None) -> plt.Figure:
        """
        Create the Charm Profile: Time Decay Battlefield chart.
        
        Args:
            charm_data (pd.DataFrame): Charm profile data
            current_price (float): Current SPX price
            charm_levels (Dict[str, float]): Key Charm levels
            save_path (Optional[str]): Path to save the chart
            
        Returns:
            plt.Figure: The generated chart
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Filter for calls and puts
        calls_data = charm_data[charm_data['option_type'] == 'call'].copy()
        puts_data = charm_data[charm_data['option_type'] == 'put'].copy()
        
        # Convert Charm to millions for display
        calls_data['charm_millions'] = calls_data['charm'] * 1000
        puts_data['charm_millions'] = puts_data['charm'] * 1000
        
        # Create background regions
        strike_min = charm_data['strike'].min()
        strike_max = charm_data['strike'].max()
        charm_min = min(calls_data['charm_millions'].min(), puts_data['charm_millions'].min())
        charm_max = max(calls_data['charm_millions'].max(), puts_data['charm_millions'].max())
        
        # Positive charm region (left side)
        positive_region = Rectangle((strike_min, 0), current_price - strike_min, charm_max,
                                  facecolor=self.colors['background_positive'], alpha=0.3, zorder=0)
        ax.add_patch(positive_region)
        
        # Negative charm region (right side)
        negative_region = Rectangle((current_price, charm_min), strike_max - current_price, -charm_min,
                                  facecolor=self.colors['background_negative'], alpha=0.3, zorder=0)
        ax.add_patch(negative_region)
        
        # Plot Charm profiles
        ax.plot(calls_data['strike'], calls_data['charm_millions'], 
               color=self.colors['positive_charm'], linewidth=3, label='Calls Charm', zorder=2)
        ax.plot(puts_data['strike'], puts_data['charm_millions'], 
               color=self.colors['negative_charm'], linewidth=3, label='Puts Charm', zorder=2)
        
        # Add critical level line
        ax.axvline(x=current_price, color=self.colors['critical_line'], 
                  linestyle='--', linewidth=2, label=f'SPX {current_price:.0f}', zorder=3)
        
        # Add zero line
        ax.axhline(y=0, color='black', linewidth=1, alpha=0.5, zorder=1)
        
        # Add annotations
        ax.text(0.02, 0.98, 'POSITIVE CHARM\nTime helps positions', 
               transform=ax.transAxes, fontsize=12, fontweight='bold',
               color=self.colors['positive_charm'], verticalalignment='top',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        ax.text(0.98, 0.02, 'NEGATIVE CHARM\nTime destroys positions', 
               transform=ax.transAxes, fontsize=12, fontweight='bold',
               color=self.colors['negative_charm'], verticalalignment='bottom',
               horizontalalignment='right',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # Add critical discovery box
        critical_text = f"CRITICAL: Charm flips from ally to enemy at {current_price:.0f} - Creating explosive conditions"
        ax.text(0.5, 0.95, critical_text, transform=ax.transAxes, fontsize=11,
               color=self.colors['text_critical'], horizontalalignment='center',
               bbox=dict(boxstyle="round,pad=0.5", facecolor='#FFE4E1', edgecolor=self.colors['text_critical']))
        
        # Styling
        ax.set_xlabel('Strike Price', fontsize=14, fontweight='bold')
        ax.set_ylabel('Charm (x 0) - Millions', fontsize=14, fontweight='bold')
        ax.set_title('Charm Profile: The Time Decay Battlefield', fontsize=18, fontweight='bold', pad=20)
        ax.legend(loc='upper right', fontsize=12)
        ax.grid(True, alpha=0.3)
        
        # Format axes
        ax.ticklabel_format(style='plain', axis='x')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Charm Profile chart saved to {save_path}")
        
        return fig
    
    def create_gamma_charm_interaction_chart(self, interaction_data: pd.DataFrame,
                                           current_price: float,
                                           save_path: Optional[str] = None) -> plt.Figure:
        """
        Create the Gamma-Charm Interaction scatter plot.
        
        Args:
            interaction_data (pd.DataFrame): Gamma-Charm interaction data
            current_price (float): Current SPX price
            save_path (Optional[str]): Path to save the chart
            
        Returns:
            plt.Figure: The generated chart
        """
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Separate calls and puts
        calls_data = interaction_data[interaction_data['option_type'] == 'c'].copy()
        puts_data = interaction_data[interaction_data['option_type'] == 'p'].copy()
        
        # Create size mapping based on position size
        def size_mapper(position_size):
            return np.clip(position_size / 1000, 50, 1000)  # Scale for visibility
        
        # Plot calls
        if len(calls_data) > 0:
            scatter_calls = ax.scatter(calls_data['strike'], calls_data['gamma'], 
                                     s=calls_data['position_size'].apply(size_mapper),
                                     c=calls_data['charm'], cmap='RdYlGn', 
                                     alpha=0.7, edgecolors='black', linewidth=0.5,
                                     label='Calls')
        
        # Plot puts
        if len(puts_data) > 0:
            scatter_puts = ax.scatter(puts_data['strike'], puts_data['gamma'], 
                                    s=puts_data['position_size'].apply(size_mapper),
                                    c=puts_data['charm'], cmap='RdYlGn', 
                                    alpha=0.7, edgecolors='black', linewidth=0.5,
                                    marker='^', label='Puts')
        
        # Add current price line
        ax.axvline(x=current_price, color=self.colors['critical_line'], 
                  linestyle='--', linewidth=2, label=f'SPX {current_price:.0f}')
        
        # Add annotations for key regions
        ax.text(0.02, 0.98, 'Gamma + Positive Charm = Stability', 
               transform=ax.transAxes, fontsize=11, fontweight='bold',
               color=self.colors['positive_charm'], verticalalignment='top',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        ax.text(0.98, 0.02, 'Gamma + Negative Charm = EXPLOSION', 
               transform=ax.transAxes, fontsize=11, fontweight='bold',
               color=self.colors['negative_charm'], verticalalignment='bottom',
               horizontalalignment='right',
               bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # Add colorbar
        if len(calls_data) > 0 or len(puts_data) > 0:
            cbar = plt.colorbar(scatter_calls if len(calls_data) > 0 else scatter_puts, ax=ax)
            cbar.set_label('Charm Size & Color:', fontsize=12, fontweight='bold')
            cbar.ax.tick_params(labelsize=10)
        
        # Add size legend
        sizes = [500, 1000, 3000]
        size_labels = ['500M', '1000M', '3000M']
        legend_elements = []
        for size, label in zip(sizes, size_labels):
            legend_elements.append(plt.scatter([], [], s=size/10, c='gray', alpha=0.7, 
                                             edgecolors='black', linewidth=0.5, label=label))
        
        size_legend = ax.legend(handles=legend_elements, title='Position Size', 
                              loc='upper left', fontsize=10, title_fontsize=11)
        ax.add_artist(size_legend)
        
        # Main legend
        ax.legend(loc='upper right', fontsize=12)
        
        # Styling
        ax.set_xlabel('Strike Price', fontsize=14, fontweight='bold')
        ax.set_ylabel('Gamma (Blue Bars)', fontsize=14, fontweight='bold')
        ax.set_title('The Gamma-Charm Interaction', fontsize=18, fontweight='bold', pad=20)
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Gamma-Charm Interaction chart saved to {save_path}")
        
        return fig
    
    def create_vanna_charm_double_trap_chart(self, vanna_charm_data: pd.DataFrame,
                                           current_price: float,
                                           save_path: Optional[str] = None) -> plt.Figure:
        """
        Create the Vanna-Charm Double Trap chart.
        
        Args:
            vanna_charm_data (pd.DataFrame): Vanna-Charm data
            current_price (float): Current SPX price
            save_path (Optional[str]): Path to save the chart
            
        Returns:
            plt.Figure: The generated chart
        """
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.figsize[0], self.figsize[1]*1.2), 
                                      sharex=True, gridspec_kw={'height_ratios': [1, 3]})
        
        # Top panel: Regime indicators
        stable_data = vanna_charm_data[vanna_charm_data['regime'] == 'stable']
        explosive_data = vanna_charm_data[vanna_charm_data['regime'] == 'explosive']
        
        # Create regime background
        if len(stable_data) > 0:
            ax1.axvspan(stable_data['strike'].min(), stable_data['strike'].max(), 
                       color=self.colors['background_positive'], alpha=0.5, label='Stable Regime')
        
        if len(explosive_data) > 0:
            ax1.axvspan(explosive_data['strike'].min(), explosive_data['strike'].max(), 
                       color=self.colors['background_negative'], alpha=0.5, label='Explosive Regime')
        
        # Add regime change line
        ax1.axvline(x=current_price, color=self.colors['critical_line'], 
                   linewidth=3, label=f'REGIME CHANGE')
        
        # Regime labels
        ax1.text(0.25, 0.5, 'Below 6185: Stable Regime (JPM\'s Safe Zone)', 
                transform=ax1.transAxes, fontsize=12, fontweight='bold',
                horizontalalignment='center', verticalalignment='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['background_positive']))
        
        ax1.text(0.75, 0.5, 'Above 6185: Explosive Regime (JPM\'s Nightmare)', 
                transform=ax1.transAxes, fontsize=12, fontweight='bold',
                horizontalalignment='center', verticalalignment='center',
                bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['background_negative']))
        
        ax1.set_ylabel('Regime', fontsize=12, fontweight='bold')
        ax1.set_ylim(-1, 1)
        ax1.set_yticks([])
        ax1.legend(loc='upper center', bbox_to_anchor=(0.5, -0.1), ncol=3, fontsize=10)
        
        # Bottom panel: Vanna and Charm profiles
        ax2_twin = ax2.twinx()
        
        # Plot Vanna (blue line, left axis)
        vanna_line = ax2.plot(vanna_charm_data['strike'], vanna_charm_data['vanna'], 
                             color='blue', linewidth=3, label='Vanna', zorder=2)
        
        # Plot Charm (red line, right axis)
        charm_line = ax2_twin.plot(vanna_charm_data['strike'], vanna_charm_data['charm'], 
                                  color='red', linewidth=3, label='Charm', zorder=2)
        
        # Add regime backgrounds
        strike_min = vanna_charm_data['strike'].min()
        strike_max = vanna_charm_data['strike'].max()
        vanna_min = vanna_charm_data['vanna'].min()
        vanna_max = vanna_charm_data['vanna'].max()
        charm_min = vanna_charm_data['charm'].min()
        charm_max = vanna_charm_data['charm'].max()
        
        # Stable regime background
        stable_bg = Rectangle((strike_min, vanna_min), current_price - strike_min, vanna_max - vanna_min,
                            facecolor=self.colors['background_positive'], alpha=0.2, zorder=0)
        ax2.add_patch(stable_bg)
        
        # Explosive regime background
        explosive_bg = Rectangle((current_price, vanna_min), strike_max - current_price, vanna_max - vanna_min,
                               facecolor=self.colors['background_negative'], alpha=0.2, zorder=0)
        ax2.add_patch(explosive_bg)
        
        # Add critical line
        ax2.axvline(x=current_price, color=self.colors['critical_line'], 
                   linestyle='--', linewidth=2, zorder=3)
        
        # Add key annotations
        ax2.annotate('Vanna: +23K\nEXPLOSIVE REGIME', 
                    xy=(current_price + 20, vanna_charm_data['vanna'].max() * 0.8),
                    xytext=(current_price + 40, vanna_charm_data['vanna'].max() * 0.9),
                    fontsize=11, fontweight='bold', color='blue',
                    arrowprops=dict(arrowstyle='->', color='blue', lw=2),
                    bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        ax2_twin.annotate('Charm: -3.5B', 
                         xy=(current_price + 30, charm_min * 0.8),
                         xytext=(current_price + 50, charm_min * 0.6),
                         fontsize=11, fontweight='bold', color='red',
                         arrowprops=dict(arrowstyle='->', color='red', lw=2),
                         bbox=dict(boxstyle="round,pad=0.3", facecolor='white', alpha=0.8))
        
        # Styling
        ax2.set_xlabel('Strike Price', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Vanna (Thousands)', fontsize=14, fontweight='bold', color='blue')
        ax2_twin.set_ylabel('Charm (Millions)', fontsize=14, fontweight='bold', color='red')
        
        # Color the y-axis labels
        ax2.tick_params(axis='y', labelcolor='blue')
        ax2_twin.tick_params(axis='y', labelcolor='red')
        
        # Combined legend
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax2_twin.get_legend_handles_labels()
        ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=12)
        
        ax2.grid(True, alpha=0.3)
        
        # Main title
        fig.suptitle('The Vanna-Charm Double Trap at 6185\nHow Two Greeks Create the Perfect Trap', 
                    fontsize=18, fontweight='bold', y=0.95)
        
        # Critical discovery box
        critical_text = ("CRITICAL DISCOVERY: Both Vanna and Charm flip at 6185, creating a regime change that JPM must defend FROM BELOW. "
                        "They desperately need SPX to stay under 6185 to avoid the doom loop!")
        fig.text(0.5, 0.02, critical_text, fontsize=11, color=self.colors['text_critical'], 
                horizontalalignment='center', wrap=True,
                bbox=dict(boxstyle="round,pad=0.5", facecolor='#FFE4E1', edgecolor=self.colors['text_critical']))
        
        plt.tight_layout()
        plt.subplots_adjust(top=0.9, bottom=0.15)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Vanna-Charm Double Trap chart saved to {save_path}")
        
        return fig
    
    def create_summary_dashboard(self, charm_data: pd.DataFrame,
                               interaction_data: pd.DataFrame,
                               vanna_charm_data: pd.DataFrame,
                               current_price: float,
                               charm_levels: Dict[str, float],
                               save_path: Optional[str] = None) -> plt.Figure:
        """
        Create a summary dashboard with all three charts.
        
        Args:
            charm_data (pd.DataFrame): Charm profile data
            interaction_data (pd.DataFrame): Gamma-Charm interaction data
            vanna_charm_data (pd.DataFrame): Vanna-Charm data
            current_price (float): Current SPX price
            charm_levels (Dict[str, float]): Key Charm levels
            save_path (Optional[str]): Path to save the chart
            
        Returns:
            plt.Figure: The generated dashboard
        """
        fig = plt.figure(figsize=(20, 16))
        
        # Create subplots
        gs = fig.add_gridspec(3, 2, height_ratios=[1, 1, 1], width_ratios=[1, 1], 
                             hspace=0.3, wspace=0.3)
        
        # Chart 1: Charm Profile (top left)
        ax1 = fig.add_subplot(gs[0, :])
        self._plot_charm_profile_subplot(ax1, charm_data, current_price, charm_levels)
        
        # Chart 2: Gamma-Charm Interaction (middle left)
        ax2 = fig.add_subplot(gs[1, 0])
        self._plot_gamma_charm_subplot(ax2, interaction_data, current_price)
        
        # Chart 3: Vanna-Charm Double Trap (middle right and bottom)
        ax3 = fig.add_subplot(gs[1, 1])
        ax4 = fig.add_subplot(gs[2, :])
        self._plot_vanna_charm_subplot(ax3, ax4, vanna_charm_data, current_price)
        
        # Main title
        fig.suptitle('SPX Options Analytics Dashboard - The Complete Picture', 
                    fontsize=24, fontweight='bold', y=0.98)
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"Summary dashboard saved to {save_path}")
        
        return fig
    
    def _plot_charm_profile_subplot(self, ax, charm_data, current_price, charm_levels):
        """Helper method to plot Charm profile in a subplot."""
        calls_data = charm_data[charm_data['option_type'] == 'call'].copy()
        calls_data['charm_millions'] = calls_data['charm'] * 1000
        
        ax.plot(calls_data['strike'], calls_data['charm_millions'], 
               color=self.colors['positive_charm'], linewidth=2)
        ax.axvline(x=current_price, color=self.colors['critical_line'], 
                  linestyle='--', linewidth=2)
        ax.set_title('Charm Profile: Time Decay Battlefield', fontsize=14, fontweight='bold')
        ax.set_xlabel('Strike Price')
        ax.set_ylabel('Charm (Millions)')
        ax.grid(True, alpha=0.3)
    
    def _plot_gamma_charm_subplot(self, ax, interaction_data, current_price):
        """Helper method to plot Gamma-Charm interaction in a subplot."""
        calls_data = interaction_data[interaction_data['option_type'] == 'c']
        
        if len(calls_data) > 0:
            ax.scatter(calls_data['strike'], calls_data['gamma'], 
                      s=50, c=calls_data['charm'], cmap='RdYlGn', alpha=0.7)
        
        ax.axvline(x=current_price, color=self.colors['critical_line'], 
                  linestyle='--', linewidth=2)
        ax.set_title('Gamma-Charm Interaction', fontsize=14, fontweight='bold')
        ax.set_xlabel('Strike Price')
        ax.set_ylabel('Gamma')
        ax.grid(True, alpha=0.3)
    
    def _plot_vanna_charm_subplot(self, ax1, ax2, vanna_charm_data, current_price):
        """Helper method to plot Vanna-Charm double trap in subplots."""
        # Regime indicator (top)
        ax1.axvline(x=current_price, color=self.colors['critical_line'], linewidth=2)
        ax1.set_title('Vanna-Charm Double Trap', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Regime')
        ax1.set_ylim(-1, 1)
        ax1.set_yticks([])
        
        # Vanna and Charm profiles (bottom)
        ax2_twin = ax2.twinx()
        ax2.plot(vanna_charm_data['strike'], vanna_charm_data['vanna'], 
                color='blue', linewidth=2, label='Vanna')
        ax2_twin.plot(vanna_charm_data['strike'], vanna_charm_data['charm'], 
                     color='red', linewidth=2, label='Charm')
        ax2.axvline(x=current_price, color=self.colors['critical_line'], 
                   linestyle='--', linewidth=2)
        ax2.set_xlabel('Strike Price')
        ax2.set_ylabel('Vanna', color='blue')
        ax2_twin.set_ylabel('Charm', color='red')
        ax2.grid(True, alpha=0.3)


def main():
    """
    Example usage of the ChartGenerator class.
    """
    from data_loader import OptionsDataLoader
    from analytics_engine import OptionsAnalyticsEngine
    
    # Initialize components
    loader = OptionsDataLoader('/home/<USER>/upload/spx_complete_2025_q2.csv')
    analytics = OptionsAnalyticsEngine()
    charts = ChartGenerator()
    
    # Load and filter data
    print("Loading and filtering data...")
    data = loader.load_data()
    date_data = loader.filter_by_date('2025-04-01')
    
    current_price = date_data['spx_close'].iloc[0]
    filtered_data = loader.filter_by_strike_range(date_data, 
                                                 current_price - 100, 
                                                 current_price + 100)
    filtered_data = loader.filter_by_dte_range(filtered_data, 1, 30)
    
    print(f"Current SPX price: {current_price}")
    print(f"Working with {len(filtered_data)} options")
    
    # Calculate analytics
    print("Calculating analytics...")
    charm_profile = analytics.calculate_charm_profile(
        filtered_data, (current_price - 50, current_price + 50))
    
    gamma_charm = analytics.calculate_gamma_charm_interaction(filtered_data)
    
    vanna_charm = analytics.calculate_vanna_charm_double_trap(
        filtered_data, (current_price - 50, current_price + 50))
    
    charm_levels = analytics.get_charm_bomb_levels(current_price, filtered_data)
    
    # Generate charts
    print("Generating charts...")
    
    # Chart 1: Charm Profile
    fig1 = charts.create_charm_profile_chart(
        charm_profile, current_price, charm_levels, 
        '/home/<USER>/charm_profile_chart.png')
    
    # Chart 2: Gamma-Charm Interaction
    fig2 = charts.create_gamma_charm_interaction_chart(
        gamma_charm, current_price, 
        '/home/<USER>/gamma_charm_interaction_chart.png')
    
    # Chart 3: Vanna-Charm Double Trap
    fig3 = charts.create_vanna_charm_double_trap_chart(
        vanna_charm, current_price, 
        '/home/<USER>/vanna_charm_double_trap_chart.png')
    
    # Summary Dashboard
    fig4 = charts.create_summary_dashboard(
        charm_profile, gamma_charm, vanna_charm, 
        current_price, charm_levels,
        '/home/<USER>/options_analytics_dashboard.png')
    
    print("All charts generated successfully!")
    
    # Show the first chart
    plt.show()


if __name__ == "__main__":
    main()


"""
Options Analytics Engine Module

This module provides functionality to calculate advanced options Greeks
and portfolio metrics for SPX options analytics and chart generation.
"""

import pandas as pd
import numpy as np
from scipy.stats import norm
from typing import Dict, List, Tuple, Optional, Any
import warnings
warnings.filterwarnings('ignore')


class OptionsAnalyticsEngine:
    """
    A class to perform advanced options analytics calculations.
    
    This class provides methods to calculate Greeks, portfolio metrics,
    and specialized analytics for options trading strategies.
    """
    
    def __init__(self):
        """Initialize the analytics engine."""
        self.risk_free_rate = 0.05  # Default risk-free rate (5%)
        
    def set_risk_free_rate(self, rate: float):
        """
        Set the risk-free rate for calculations.
        
        Args:
            rate (float): Risk-free rate (e.g., 0.05 for 5%)
        """
        self.risk_free_rate = rate
    
    def black_scholes_greeks(self, S: float, K: float, T: float, r: float, 
                           sigma: float, option_type: str = 'call') -> Dict[str, float]:
        """
        Calculate Black-Scholes Greeks for a single option.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration (in years)
            r (float): Risk-free rate
            sigma (float): Implied volatility
            option_type (str): 'call' or 'put'
            
        Returns:
            Dict[str, float]: Dictionary containing all Greeks
        """
        if T <= 0 or sigma <= 0:
            return {
                'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 
                'vega': 0.0, 'rho': 0.0, 'charm': 0.0, 'vanna': 0.0
            }
        
        # Calculate d1 and d2
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        # Standard normal PDF and CDF
        N_d1 = norm.cdf(d1)
        N_d2 = norm.cdf(d2)
        n_d1 = norm.pdf(d1)
        
        # Calculate Greeks
        if option_type.lower() == 'call':
            delta = N_d1
            theta = (-S * n_d1 * sigma / (2 * np.sqrt(T)) - 
                    r * K * np.exp(-r * T) * N_d2) / 365
            rho = K * T * np.exp(-r * T) * N_d2 / 100
        else:  # put
            delta = N_d1 - 1
            theta = (-S * n_d1 * sigma / (2 * np.sqrt(T)) + 
                    r * K * np.exp(-r * T) * norm.cdf(-d2)) / 365
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
        
        gamma = n_d1 / (S * sigma * np.sqrt(T))
        vega = S * n_d1 * np.sqrt(T) / 100
        
        # Second-order Greeks
        charm = -n_d1 * (2 * r * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T))
        if option_type.lower() == 'put':
            charm = charm - n_d1 / (sigma * np.sqrt(T))
            
        vanna = vega * d2 / sigma
        
        return {
            'delta': delta,
            'gamma': gamma,
            'theta': theta,
            'vega': vega,
            'rho': rho,
            'charm': charm,
            'vanna': vanna
        }
    
    def calculate_portfolio_greeks(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Greeks for a portfolio of options.
        
        Args:
            options_data (pd.DataFrame): DataFrame with options data
            
        Returns:
            pd.DataFrame: DataFrame with calculated Greeks
        """
        results = []
        
        for _, row in options_data.iterrows():
            # Extract parameters
            S = row['spx_close']
            K = row['Strike']
            T = max(row['T'], 1/365)  # Minimum 1 day
            r = self.risk_free_rate
            
            # Use implied volatility if available, otherwise estimate
            if 'Bid Implied Volatility' in row and row['Bid Implied Volatility'] > 0:
                sigma = row['Bid Implied Volatility']
            elif 'Ask Implied Volatility' in row and row['Ask Implied Volatility'] > 0:
                sigma = row['Ask Implied Volatility']
            else:
                # Estimate volatility based on moneyness and time
                moneyness = K / S
                sigma = max(0.1, 0.2 + 0.1 * abs(moneyness - 1) + 0.05 / max(T, 0.01))
            
            option_type = 'call' if row['Call/Put'] == 'c' else 'put'
            
            # Calculate Greeks
            greeks = self.black_scholes_greeks(S, K, T, r, sigma, option_type)
            
            # Create result row
            result_row = row.copy()
            for greek, value in greeks.items():
                result_row[f'calc_{greek}'] = value
            
            results.append(result_row)
        
        return pd.DataFrame(results)
    
    def calculate_charm_profile(self, options_data: pd.DataFrame, 
                              strike_range: Tuple[float, float],
                              num_points: int = 100) -> pd.DataFrame:
        """
        Calculate Charm profile across a range of strike prices.
        
        Args:
            options_data (pd.DataFrame): Options data for a specific date/expiry
            strike_range (Tuple[float, float]): (min_strike, max_strike)
            num_points (int): Number of points to calculate
            
        Returns:
            pd.DataFrame: Charm profile data
        """
        min_strike, max_strike = strike_range
        strike_points = np.linspace(min_strike, max_strike, num_points)
        
        # Get representative option parameters
        sample_row = options_data.iloc[0]
        S = sample_row['spx_close']
        T = max(sample_row['T'], 1/365)
        r = self.risk_free_rate
        
        results = []
        
        for strike in strike_points:
            # Calculate for both calls and puts
            for option_type in ['call', 'put']:
                # Estimate volatility
                moneyness = strike / S
                sigma = max(0.1, 0.2 + 0.1 * abs(moneyness - 1) + 0.05 / max(T, 0.01))
                
                greeks = self.black_scholes_greeks(S, strike, T, r, sigma, option_type)
                
                results.append({
                    'strike': strike,
                    'option_type': option_type,
                    'charm': greeks['charm'],
                    'gamma': greeks['gamma'],
                    'vanna': greeks['vanna'],
                    'delta': greeks['delta'],
                    'moneyness': moneyness,
                    'spot_price': S
                })
        
        return pd.DataFrame(results)
    
    def calculate_gamma_charm_interaction(self, options_data: pd.DataFrame) -> pd.DataFrame:
        """
        Calculate Gamma-Charm interaction data for scatter plot.
        
        Args:
            options_data (pd.DataFrame): Options data with calculated Greeks
            
        Returns:
            pd.DataFrame: Gamma-Charm interaction data
        """
        # Filter for options with significant open interest or volume
        significant_options = options_data[
            (options_data['Open Interest'] > 0) | (options_data['Volume'] > 0)
        ].copy()
        
        if len(significant_options) == 0:
            significant_options = options_data.copy()
        
        # Calculate portfolio Greeks
        portfolio_data = self.calculate_portfolio_greeks(significant_options)
        
        # Create interaction data
        interaction_data = []
        
        for _, row in portfolio_data.iterrows():
            charm = row['calc_charm']
            gamma = row['calc_gamma']
            
            # Calculate position size based on open interest
            position_size = max(row['Open Interest'], 1) * 100  # Convert to shares
            
            # Calculate dollar Greeks
            charm_dollars = charm * position_size
            gamma_dollars = gamma * position_size
            
            interaction_data.append({
                'strike': row['Strike'],
                'gamma': gamma_dollars,
                'charm': charm_dollars,
                'position_size': position_size,
                'option_type': row['Call/Put'],
                'moneyness': row['Moneyness'],
                'dte': row['DTE'],
                'open_interest': row['Open Interest']
            })
        
        return pd.DataFrame(interaction_data)
    
    def calculate_vanna_charm_double_trap(self, options_data: pd.DataFrame,
                                        strike_range: Tuple[float, float],
                                        num_points: int = 100) -> pd.DataFrame:
        """
        Calculate Vanna-Charm double trap data.
        
        Args:
            options_data (pd.DataFrame): Options data
            strike_range (Tuple[float, float]): (min_strike, max_strike)
            num_points (int): Number of points to calculate
            
        Returns:
            pd.DataFrame: Vanna-Charm double trap data
        """
        min_strike, max_strike = strike_range
        strike_points = np.linspace(min_strike, max_strike, num_points)
        
        # Get representative parameters
        sample_row = options_data.iloc[0]
        S = sample_row['spx_close']
        T = max(sample_row['T'], 1/365)
        r = self.risk_free_rate
        
        results = []
        
        for strike in strike_points:
            # Estimate volatility
            moneyness = strike / S
            sigma = max(0.1, 0.2 + 0.1 * abs(moneyness - 1) + 0.05 / max(T, 0.01))
            
            # Calculate for calls (primary focus)
            greeks = self.black_scholes_greeks(S, strike, T, r, sigma, 'call')
            
            # Calculate portfolio impact (assuming standard position sizes)
            position_size = 1000000  # $1M notional
            contracts = position_size / (S * 100)  # Number of contracts
            
            vanna_impact = greeks['vanna'] * contracts
            charm_impact = greeks['charm'] * contracts
            
            results.append({
                'strike': strike,
                'vanna': vanna_impact,
                'charm': charm_impact,
                'moneyness': moneyness,
                'spot_price': S,
                'regime': 'stable' if strike < S else 'explosive'
            })
        
        return pd.DataFrame(results)
    
    def get_charm_bomb_levels(self, current_price: float, 
                            options_data: pd.DataFrame) -> Dict[str, float]:
        """
        Calculate key Charm bomb levels based on current market conditions.
        
        Args:
            current_price (float): Current SPX price
            options_data (pd.DataFrame): Options data
            
        Returns:
            Dict[str, float]: Key levels and their Charm values
        """
        # Calculate Charm profile around current price
        strike_range = (current_price - 100, current_price + 100)
        charm_profile = self.calculate_charm_profile(options_data, strike_range, 200)
        
        # Find key levels
        calls_data = charm_profile[charm_profile['option_type'] == 'call']
        
        # Find where Charm flips from positive to negative
        positive_charm = calls_data[calls_data['charm'] > 0]
        negative_charm = calls_data[calls_data['charm'] < 0]
        
        flip_point = current_price  # Default
        if len(positive_charm) > 0 and len(negative_charm) > 0:
            last_positive = positive_charm['strike'].max()
            first_negative = negative_charm['strike'].min()
            flip_point = (last_positive + first_negative) / 2
        
        # Calculate Charm values at key levels
        levels = {
            'current_price': current_price,
            'charm_flip_point': flip_point,
            'charm_at_current': self._interpolate_charm(calls_data, current_price),
            'charm_at_flip': 0.0,  # By definition
            'max_positive_charm': positive_charm['charm'].max() if len(positive_charm) > 0 else 0,
            'max_negative_charm': negative_charm['charm'].min() if len(negative_charm) > 0 else 0
        }
        
        return levels
    
    def _interpolate_charm(self, charm_data: pd.DataFrame, target_strike: float) -> float:
        """
        Interpolate Charm value at a specific strike.
        
        Args:
            charm_data (pd.DataFrame): Charm profile data
            target_strike (float): Target strike price
            
        Returns:
            float: Interpolated Charm value
        """
        if len(charm_data) == 0:
            return 0.0
        
        # Find closest strikes
        below = charm_data[charm_data['strike'] <= target_strike]
        above = charm_data[charm_data['strike'] >= target_strike]
        
        if len(below) == 0:
            return above.iloc[0]['charm']
        if len(above) == 0:
            return below.iloc[-1]['charm']
        
        # Linear interpolation
        s1, c1 = below.iloc[-1]['strike'], below.iloc[-1]['charm']
        s2, c2 = above.iloc[0]['strike'], above.iloc[0]['charm']
        
        if s1 == s2:
            return c1
        
        return c1 + (c2 - c1) * (target_strike - s1) / (s2 - s1)


def main():
    """
    Example usage of the OptionsAnalyticsEngine class.
    """
    from data_loader import OptionsDataLoader
    
    # Initialize components
    loader = OptionsDataLoader('/home/<USER>/upload/spx_complete_2025_q2.csv')
    analytics = OptionsAnalyticsEngine()
    
    # Load and filter data
    data = loader.load_data()
    date_data = loader.filter_by_date('2025-04-01')
    
    # Filter for options around current price with reasonable DTE
    current_price = date_data['spx_close'].iloc[0]
    filtered_data = loader.filter_by_strike_range(date_data, 
                                                 current_price - 200, 
                                                 current_price + 200)
    filtered_data = loader.filter_by_dte_range(filtered_data, 1, 30)
    
    print(f"Current SPX price: {current_price}")
    print(f"Analyzing {len(filtered_data)} options")
    
    # Calculate Charm profile
    print("\nCalculating Charm profile...")
    charm_profile = analytics.calculate_charm_profile(
        filtered_data, 
        (current_price - 100, current_price + 100)
    )
    print(f"Charm profile calculated for {len(charm_profile)} points")
    
    # Calculate Gamma-Charm interaction
    print("\nCalculating Gamma-Charm interaction...")
    gamma_charm = analytics.calculate_gamma_charm_interaction(filtered_data)
    print(f"Gamma-Charm interaction calculated for {len(gamma_charm)} options")
    
    # Calculate Vanna-Charm double trap
    print("\nCalculating Vanna-Charm double trap...")
    vanna_charm = analytics.calculate_vanna_charm_double_trap(
        filtered_data,
        (current_price - 100, current_price + 100)
    )
    print(f"Vanna-Charm double trap calculated for {len(vanna_charm)} points")
    
    # Get Charm bomb levels
    print("\nCalculating Charm bomb levels...")
    levels = analytics.get_charm_bomb_levels(current_price, filtered_data)
    print("Key levels:")
    for key, value in levels.items():
        print(f"  {key}: {value:.2f}")


if __name__ == "__main__":
    main()


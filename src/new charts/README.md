# SPX Options Analytics System

A comprehensive Python system for analyzing SPX options data and generating professional options analytics charts. This system recreates three key options analytics visualizations:

1. **Charm Profile: The Time Decay Battlefield** - Shows how time decay (Charm) affects options positions across different strike prices
2. **Gamma-Charm Interaction** - Scatter plot showing the relationship between Gamma and Charm for portfolio analysis
3. **Vanna-Charm Double Trap** - Dual-axis chart showing how <PERSON><PERSON> and <PERSON><PERSON> create regime changes in options behavior

## Features

- **Modular Architecture**: Well-structured with separate modules for data loading, analytics, and chart generation
- **Professional Charts**: High-quality visualizations that match institutional analytics standards
- **Flexible Filtering**: Filter data by date, expiry, strike range, moneyness, and option type
- **Advanced Greeks Calculations**: Black-Scholes based calculations for Delta, Gamma, Theta, Vega, Rho, Charm, and Vanna
- **Portfolio Analytics**: Aggregate position-level Greeks for portfolio analysis
- **Command Line Interface**: Easy-to-use CLI for generating charts
- **Single Function Calls**: Each chart can be generated with a single function call

## System Architecture

```
SPX Options Analytics System
├── data_loader.py          # Data loading and filtering
├── analytics_engine.py     # Options Greeks calculations and analytics
├── chart_generator.py      # Chart generation and visualization
├── main.py                 # Main execution script and CLI
├── requirements.txt        # Python dependencies
└── README.md              # This documentation
```

### Module Overview

#### `data_loader.py` - OptionsDataLoader Class
- Load SPX options data from CSV files
- Filter by date, expiry, strike range, moneyness, option type
- Data validation and cleaning
- Calculate derived metrics (DTE, moneyness, time to expiry)

#### `analytics_engine.py` - OptionsAnalyticsEngine Class
- Black-Scholes Greeks calculations
- Portfolio-level Greeks aggregation
- Charm profile calculations across strike ranges
- Gamma-Charm interaction analysis
- Vanna-Charm double trap calculations
- Key level identification (Charm bomb levels)

#### `chart_generator.py` - ChartGenerator Class
- Professional chart styling and layout
- Charm Profile: Time Decay Battlefield chart
- Gamma-Charm Interaction scatter plot
- Vanna-Charm Double Trap dual-axis chart
- Summary dashboard with all charts
- Customizable colors, annotations, and styling

#### `main.py` - SPXOptionsAnalytics Class & CLI
- Main orchestration class
- Command-line interface
- Batch chart generation
- Data summary and exploration tools

## Installation

1. **Install Python Dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Verify Installation**:
   ```bash
   python main.py --help
   ```

## Usage

### Command Line Interface

#### Generate All Charts for a Specific Date
```bash
python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01
```

#### Generate Specific Chart Types
```bash
# Charm Profile only
python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --chart charm_profile

# Gamma-Charm Interaction only
python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --chart gamma_charm_interaction

# Vanna-Charm Double Trap only
python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --chart vanna_charm_double_trap
```

#### Custom Parameters
```bash
# Custom strike range (10% around current price) and DTE range (5-45 days)
python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 \
    --strike_range 0.1 --min_dte 5 --max_dte 45 --output_dir my_charts
```

#### Data Exploration
```bash
# List available dates
python main.py --csv_path spx_complete_2025_q2.csv --list_dates

# Show dataset summary
python main.py --csv_path spx_complete_2025_q2.csv --summary
```

### Programmatic Usage

#### Single Function Calls for Each Chart

```python
from data_loader import OptionsDataLoader
from analytics_engine import OptionsAnalyticsEngine
from chart_generator import ChartGenerator

# Initialize components
loader = OptionsDataLoader('spx_complete_2025_q2.csv')
analytics = OptionsAnalyticsEngine()
charts = ChartGenerator()

# Load and filter data
data = loader.load_data()
date_data = loader.filter_by_date('2025-04-01')
current_price = date_data['spx_close'].iloc[0]

# Filter for analysis
filtered_data = loader.filter_by_strike_range(date_data, 
                                             current_price - 100, 
                                             current_price + 100)
filtered_data = loader.filter_by_dte_range(filtered_data, 1, 30)

# Generate Charm Profile Chart
charm_profile = analytics.calculate_charm_profile(
    filtered_data, (current_price - 50, current_price + 50))
charm_levels = analytics.get_charm_bomb_levels(current_price, filtered_data)

chart1 = charts.create_charm_profile_chart(
    charm_profile, current_price, charm_levels, 'charm_profile.png')

# Generate Gamma-Charm Interaction Chart
gamma_charm = analytics.calculate_gamma_charm_interaction(filtered_data)

chart2 = charts.create_gamma_charm_interaction_chart(
    gamma_charm, current_price, 'gamma_charm_interaction.png')

# Generate Vanna-Charm Double Trap Chart
vanna_charm = analytics.calculate_vanna_charm_double_trap(
    filtered_data, (current_price - 50, current_price + 50))

chart3 = charts.create_vanna_charm_double_trap_chart(
    vanna_charm, current_price, 'vanna_charm_double_trap.png')
```

#### Using the Main Analytics Class

```python
from main import SPXOptionsAnalytics

# Initialize system
analytics_system = SPXOptionsAnalytics('spx_complete_2025_q2.csv', 'output')

# Generate all charts for a date
results = analytics_system.generate_all_charts('2025-04-01')

# Generate individual charts
charm_chart = analytics_system.generate_charm_profile_chart('2025-04-01')
gamma_chart = analytics_system.generate_gamma_charm_interaction_chart('2025-04-01')
vanna_chart = analytics_system.generate_vanna_charm_double_trap_chart('2025-04-01')
```

## Data Format

The system expects SPX options data in CSV format with the following columns:

- `date`: Trading date (YYYY-MM-DD)
- `Strike`: Strike price
- `Expiry Date`: Option expiry date (YYYY-MM-DD)
- `Call/Put`: Option type ('c' for calls, 'p' for puts)
- `spx_close`: SPX closing price
- `Open Interest`: Open interest (optional, used for position sizing)
- `Volume`: Trading volume (optional)
- `Bid Implied Volatility`: Bid IV (optional, used if available)
- `Ask Implied Volatility`: Ask IV (optional, used if available)

Additional columns are preserved but not required for the analytics.

## Chart Descriptions

### 1. Charm Profile: The Time Decay Battlefield

This chart shows how time decay (Charm) affects options positions across different strike prices:

- **X-axis**: Strike prices
- **Y-axis**: Charm values (in millions)
- **Green region**: Positive Charm (time helps positions)
- **Red region**: Negative Charm (time destroys positions)
- **Critical line**: Current SPX price where Charm flips
- **Key insight**: Shows where time decay becomes an ally vs. enemy

### 2. Gamma-Charm Interaction

A scatter plot showing the relationship between Gamma and Charm:

- **X-axis**: Strike prices
- **Y-axis**: Gamma values
- **Bubble size**: Position size (open interest)
- **Bubble color**: Charm values (green = positive, red = negative)
- **Shapes**: Circles for calls, triangles for puts
- **Key insight**: Identifies explosive vs. stable market regimes

### 3. Vanna-Charm Double Trap

A dual-axis chart showing how Vanna and Charm create regime changes:

- **Top panel**: Regime indicators (stable vs. explosive)
- **Bottom panel**: Vanna (blue, left axis) and Charm (red, right axis)
- **Critical discovery**: Both Greeks flip at the same level, creating a "double trap"
- **Key insight**: Shows how market makers get trapped by simultaneous Greek flips

## Customization

### Chart Styling

Modify colors and styling in `chart_generator.py`:

```python
self.colors = {
    'positive_charm': '#2E8B57',      # Sea green
    'negative_charm': '#DC143C',      # Crimson
    'neutral': '#4682B4',             # Steel blue
    'background_positive': '#E6F3E6', # Light green
    'background_negative': '#FFE6E6', # Light red
    'critical_line': '#FF8C00',       # Dark orange
    'text_critical': '#B22222'        # Fire brick
}
```

### Analytics Parameters

Modify calculation parameters in `analytics_engine.py`:

```python
# Risk-free rate for Greeks calculations
analytics.set_risk_free_rate(0.05)  # 5%

# Custom volatility estimation
# Modify the volatility estimation logic in black_scholes_greeks()
```

### Data Filtering

Customize filtering logic in `data_loader.py`:

```python
# Add custom filters
def filter_by_volume(self, data, min_volume):
    return data[data['Volume'] >= min_volume]

def filter_by_open_interest(self, data, min_oi):
    return data[data['Open Interest'] >= min_oi]
```

## Performance Notes

- **Large datasets**: The system handles large datasets efficiently but may take time for initial loading
- **Memory usage**: Keep strike ranges reasonable for large datasets
- **Chart generation**: High-DPI charts (300 DPI) may take longer to generate
- **Parallel processing**: Consider adding multiprocessing for batch date analysis

## Troubleshooting

### Common Issues

1. **"No data found for date"**: Check available dates with `--list_dates`
2. **Empty charts**: Verify strike range and DTE filters aren't too restrictive
3. **Memory errors**: Reduce strike range or date range for large datasets
4. **Import errors**: Ensure all dependencies are installed with `pip install -r requirements.txt`

### Debug Mode

Add debug prints by modifying the logging level:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## License

This system is provided as-is for educational and research purposes.

## Contributing

To extend the system:

1. Add new analytics methods to `analytics_engine.py`
2. Add new chart types to `chart_generator.py`
3. Extend filtering capabilities in `data_loader.py`
4. Update the CLI in `main.py` for new features

## Support

For questions or issues, please refer to the code documentation and comments within each module.


"""
SPX Options Analytics System - Main Execution Script

This script provides a complete system for analyzing SPX options data and generating
the three main analytics charts:
1. Charm Profile: The Time Decay Battlefield
2. Gamma-Charm Interaction scatter plot  
3. <PERSON><PERSON>-Charm Double Trap chart

Usage:
    python main.py --csv_path <path_to_csv> --date <YYYY-MM-DD> [options]

Example:
    python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01
"""

import argparse
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any

# Import our modules
from data_loader import OptionsDataLoader
from analytics_engine import OptionsAnalyticsEngine
from chart_generator import ChartGenerator


class SPXOptionsAnalytics:
    """
    Main class that orchestrates the entire options analytics workflow.
    """
    
    def __init__(self, csv_path: str, output_dir: str = "output"):
        """
        Initialize the analytics system.
        
        Args:
            csv_path (str): Path to the SPX options CSV file
            output_dir (str): Directory to save output charts
        """
        self.csv_path = csv_path
        self.output_dir = output_dir
        
        # Initialize components
        self.loader = OptionsDataLoader(csv_path)
        self.analytics = OptionsAnalyticsEngine()
        self.charts = ChartGenerator()
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"SPX Options Analytics System initialized")
        print(f"Data source: {csv_path}")
        print(f"Output directory: {output_dir}")
    
    def generate_charm_profile_chart(self, date: str, 
                                   strike_range_pct: float = 0.05,
                                   dte_range: tuple = (1, 30)) -> str:
        """
        Generate the Charm Profile: Time Decay Battlefield chart.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            strike_range_pct (float): Strike range as percentage of current price
            dte_range (tuple): (min_dte, max_dte) days to expiry range
            
        Returns:
            str: Path to the generated chart
        """
        print(f"\n=== Generating Charm Profile Chart for {date} ===")
        
        # Load and filter data
        date_data = self.loader.filter_by_date(date)
        if len(date_data) == 0:
            raise ValueError(f"No data found for date {date}")
        
        current_price = date_data['spx_close'].iloc[0]
        strike_range = current_price * strike_range_pct
        
        filtered_data = self.loader.filter_by_strike_range(
            date_data, current_price - strike_range, current_price + strike_range)
        filtered_data = self.loader.filter_by_dte_range(
            filtered_data, dte_range[0], dte_range[1])
        
        print(f"Current SPX price: {current_price:.2f}")
        print(f"Analyzing {len(filtered_data)} options")
        
        # Calculate analytics
        charm_profile = self.analytics.calculate_charm_profile(
            filtered_data, (current_price - strike_range/2, current_price + strike_range/2))
        
        charm_levels = self.analytics.get_charm_bomb_levels(current_price, filtered_data)
        
        # Generate chart
        output_path = os.path.join(self.output_dir, f"charm_profile_{date}.png")
        self.charts.create_charm_profile_chart(
            charm_profile, current_price, charm_levels, output_path)
        
        return output_path
    
    def generate_gamma_charm_interaction_chart(self, date: str,
                                             strike_range_pct: float = 0.05,
                                             dte_range: tuple = (1, 30)) -> str:
        """
        Generate the Gamma-Charm Interaction scatter plot.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            strike_range_pct (float): Strike range as percentage of current price
            dte_range (tuple): (min_dte, max_dte) days to expiry range
            
        Returns:
            str: Path to the generated chart
        """
        print(f"\n=== Generating Gamma-Charm Interaction Chart for {date} ===")
        
        # Load and filter data
        date_data = self.loader.filter_by_date(date)
        current_price = date_data['spx_close'].iloc[0]
        strike_range = current_price * strike_range_pct
        
        filtered_data = self.loader.filter_by_strike_range(
            date_data, current_price - strike_range, current_price + strike_range)
        filtered_data = self.loader.filter_by_dte_range(
            filtered_data, dte_range[0], dte_range[1])
        
        print(f"Analyzing {len(filtered_data)} options")
        
        # Calculate analytics
        gamma_charm = self.analytics.calculate_gamma_charm_interaction(filtered_data)
        
        # Generate chart
        output_path = os.path.join(self.output_dir, f"gamma_charm_interaction_{date}.png")
        self.charts.create_gamma_charm_interaction_chart(
            gamma_charm, current_price, output_path)
        
        return output_path
    
    def generate_vanna_charm_double_trap_chart(self, date: str,
                                             strike_range_pct: float = 0.05,
                                             dte_range: tuple = (1, 30)) -> str:
        """
        Generate the Vanna-Charm Double Trap chart.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            strike_range_pct (float): Strike range as percentage of current price
            dte_range (tuple): (min_dte, max_dte) days to expiry range
            
        Returns:
            str: Path to the generated chart
        """
        print(f"\n=== Generating Vanna-Charm Double Trap Chart for {date} ===")
        
        # Load and filter data
        date_data = self.loader.filter_by_date(date)
        current_price = date_data['spx_close'].iloc[0]
        strike_range = current_price * strike_range_pct
        
        filtered_data = self.loader.filter_by_strike_range(
            date_data, current_price - strike_range, current_price + strike_range)
        filtered_data = self.loader.filter_by_dte_range(
            filtered_data, dte_range[0], dte_range[1])
        
        print(f"Analyzing {len(filtered_data)} options")
        
        # Calculate analytics
        vanna_charm = self.analytics.calculate_vanna_charm_double_trap(
            filtered_data, (current_price - strike_range/2, current_price + strike_range/2))
        
        # Generate chart
        output_path = os.path.join(self.output_dir, f"vanna_charm_double_trap_{date}.png")
        self.charts.create_vanna_charm_double_trap_chart(
            vanna_charm, current_price, output_path)
        
        return output_path
    
    def generate_all_charts(self, date: str,
                          strike_range_pct: float = 0.05,
                          dte_range: tuple = (1, 30)) -> Dict[str, str]:
        """
        Generate all three charts for a given date.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            strike_range_pct (float): Strike range as percentage of current price
            dte_range (tuple): (min_dte, max_dte) days to expiry range
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        print(f"\n{'='*60}")
        print(f"GENERATING ALL CHARTS FOR {date}")
        print(f"{'='*60}")
        
        # Load data once
        if not self.loader.loaded:
            self.loader.load_data()
        
        results = {}
        
        try:
            # Generate individual charts
            results['charm_profile'] = self.generate_charm_profile_chart(
                date, strike_range_pct, dte_range)
            
            results['gamma_charm_interaction'] = self.generate_gamma_charm_interaction_chart(
                date, strike_range_pct, dte_range)
            
            results['vanna_charm_double_trap'] = self.generate_vanna_charm_double_trap_chart(
                date, strike_range_pct, dte_range)
            
            # Generate summary dashboard
            print(f"\n=== Generating Summary Dashboard ===")
            
            # Get data for dashboard
            date_data = self.loader.filter_by_date(date)
            current_price = date_data['spx_close'].iloc[0]
            strike_range = current_price * strike_range_pct
            
            filtered_data = self.loader.filter_by_strike_range(
                date_data, current_price - strike_range, current_price + strike_range)
            filtered_data = self.loader.filter_by_dte_range(
                filtered_data, dte_range[0], dte_range[1])
            
            # Calculate all analytics
            charm_profile = self.analytics.calculate_charm_profile(
                filtered_data, (current_price - strike_range/2, current_price + strike_range/2))
            gamma_charm = self.analytics.calculate_gamma_charm_interaction(filtered_data)
            vanna_charm = self.analytics.calculate_vanna_charm_double_trap(
                filtered_data, (current_price - strike_range/2, current_price + strike_range/2))
            charm_levels = self.analytics.get_charm_bomb_levels(current_price, filtered_data)
            
            # Generate dashboard
            dashboard_path = os.path.join(self.output_dir, f"options_analytics_dashboard_{date}.png")
            self.charts.create_summary_dashboard(
                charm_profile, gamma_charm, vanna_charm, 
                current_price, charm_levels, dashboard_path)
            
            results['dashboard'] = dashboard_path
            
            print(f"\n{'='*60}")
            print(f"ALL CHARTS GENERATED SUCCESSFULLY!")
            print(f"{'='*60}")
            
            for chart_name, path in results.items():
                print(f"{chart_name}: {path}")
            
            return results
            
        except Exception as e:
            print(f"Error generating charts: {e}")
            raise
    
    def get_available_dates(self) -> list:
        """
        Get list of available dates in the dataset.
        
        Returns:
            list: List of available dates
        """
        if not self.loader.loaded:
            self.loader.load_data()
        
        return self.loader.get_unique_dates()
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get summary of the loaded dataset.
        
        Returns:
            Dict[str, Any]: Data summary
        """
        if not self.loader.loaded:
            self.loader.load_data()
        
        return self.loader.get_data_summary()


def main():
    """
    Main function to run the SPX Options Analytics System from command line.
    """
    parser = argparse.ArgumentParser(
        description="SPX Options Analytics System - Generate options analytics charts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate all charts for a specific date
  python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01
  
  # Generate only Charm profile chart
  python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --chart charm_profile
  
  # Use custom strike range and DTE range
  python main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --strike_range 0.1 --min_dte 5 --max_dte 45
  
  # List available dates
  python main.py --csv_path spx_complete_2025_q2.csv --list_dates
        """)
    
    parser.add_argument('--csv_path', required=True, 
                       help='Path to the SPX options CSV file')
    parser.add_argument('--date', 
                       help='Analysis date in YYYY-MM-DD format')
    parser.add_argument('--chart', choices=['charm_profile', 'gamma_charm_interaction', 'vanna_charm_double_trap', 'all'],
                       default='all', help='Which chart(s) to generate (default: all)')
    parser.add_argument('--output_dir', default='output',
                       help='Output directory for charts (default: output)')
    parser.add_argument('--strike_range', type=float, default=0.05,
                       help='Strike range as percentage of current price (default: 0.05)')
    parser.add_argument('--min_dte', type=int, default=1,
                       help='Minimum days to expiry (default: 1)')
    parser.add_argument('--max_dte', type=int, default=30,
                       help='Maximum days to expiry (default: 30)')
    parser.add_argument('--list_dates', action='store_true',
                       help='List available dates in the dataset')
    parser.add_argument('--summary', action='store_true',
                       help='Show dataset summary')
    
    args = parser.parse_args()
    
    # Validate CSV file exists
    if not os.path.exists(args.csv_path):
        print(f"Error: CSV file not found: {args.csv_path}")
        sys.exit(1)
    
    # Initialize the analytics system
    try:
        analytics_system = SPXOptionsAnalytics(args.csv_path, args.output_dir)
    except Exception as e:
        print(f"Error initializing analytics system: {e}")
        sys.exit(1)
    
    # Handle list dates request
    if args.list_dates:
        print("\nAvailable dates in dataset:")
        dates = analytics_system.get_available_dates()
        for date in dates[:20]:  # Show first 20 dates
            print(f"  {date}")
        if len(dates) > 20:
            print(f"  ... and {len(dates) - 20} more dates")
        print(f"\nTotal: {len(dates)} dates available")
        return
    
    # Handle summary request
    if args.summary:
        print("\nDataset Summary:")
        summary = analytics_system.get_data_summary()
        for key, value in summary.items():
            print(f"  {key}: {value}")
        return
    
    # Validate date is provided for chart generation
    if not args.date:
        print("Error: --date is required for chart generation")
        print("Use --list_dates to see available dates")
        sys.exit(1)
    
    # Validate date format
    try:
        datetime.strptime(args.date, '%Y-%m-%d')
    except ValueError:
        print(f"Error: Invalid date format. Use YYYY-MM-DD format")
        sys.exit(1)
    
    # Generate charts
    try:
        dte_range = (args.min_dte, args.max_dte)
        
        if args.chart == 'all':
            results = analytics_system.generate_all_charts(
                args.date, args.strike_range, dte_range)
        elif args.chart == 'charm_profile':
            path = analytics_system.generate_charm_profile_chart(
                args.date, args.strike_range, dte_range)
            results = {'charm_profile': path}
        elif args.chart == 'gamma_charm_interaction':
            path = analytics_system.generate_gamma_charm_interaction_chart(
                args.date, args.strike_range, dte_range)
            results = {'gamma_charm_interaction': path}
        elif args.chart == 'vanna_charm_double_trap':
            path = analytics_system.generate_vanna_charm_double_trap_chart(
                args.date, args.strike_range, dte_range)
            results = {'vanna_charm_double_trap': path}
        
        print(f"\nChart generation completed successfully!")
        print(f"Output files:")
        for chart_name, path in results.items():
            print(f"  {chart_name}: {path}")
            
    except Exception as e:
        print(f"Error generating charts: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()


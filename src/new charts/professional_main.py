"""
SPX Options Analytics System - Professional Main Script

This script generates high-quality, professional charts that match the original
styling with polished visual design and exact fidelity.
"""

import argparse
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any

# Import our modules
from data_loader import OptionsDataLoader
from analytics_engine import OptionsAnalyticsEngine
from professional_chart_generator import ProfessionalChartGenerator


class ProfessionalSPXOptionsAnalytics:
    """
    Main class that generates professional, high-quality charts.
    """
    
    def __init__(self, csv_path: str, output_dir: str = "professional_output"):
        """
        Initialize the professional analytics system.
        
        Args:
            csv_path (str): Path to the SPX options CSV file
            output_dir (str): Directory to save output charts
        """
        self.csv_path = csv_path
        self.output_dir = output_dir
        
        # Initialize components
        self.loader = OptionsDataLoader(csv_path)
        self.analytics = OptionsAnalyticsEngine()
        self.charts = ProfessionalChartGenerator()
        
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        print(f"Professional SPX Options Analytics System initialized")
        print(f"Data source: {csv_path}")
        print(f"Output directory: {output_dir}")
    
    def generate_professional_charm_bomb_chart(self, date: str, 
                                             current_price: float = 6181) -> str:
        """
        Generate a professional "Charm Bomb Above 6185" chart.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            current_price (float): Current SPX price (default matches original)
            
        Returns:
            str: Path to the generated chart
        """
        print(f"\n=== Generating Professional Charm Bomb Chart ===")
        
        # Load data
        if not self.loader.loaded:
            self.loader.load_data()
        
        date_data = self.loader.filter_by_date(date)
        if len(date_data) == 0:
            raise ValueError(f"No data found for date {date}")
        
        filtered_data = self.loader.filter_by_strike_range(
            date_data, current_price - 50, current_price + 50)
        
        # Calculate analytics
        charm_profile = self.analytics.calculate_charm_profile(
            filtered_data, (current_price - 35, current_price + 35))
        
        # Generate professional chart
        output_path = os.path.join(self.output_dir, f"charm_bomb_professional_{date}.png")
        self.charts.create_professional_charm_bomb_chart(charm_profile, current_price, output_path)
        
        return output_path
    
    def generate_professional_gamma_charm_interaction_chart(self, date: str,
                                                          current_price: float = 6181) -> str:
        """
        Generate a professional "Gamma-Charm Interaction" chart.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            current_price (float): Current SPX price (default matches original)
            
        Returns:
            str: Path to the generated chart
        """
        print(f"\n=== Generating Professional Gamma-Charm Interaction Chart ===")
        
        # Load data
        if not self.loader.loaded:
            self.loader.load_data()
        
        date_data = self.loader.filter_by_date(date)
        filtered_data = self.loader.filter_by_strike_range(
            date_data, current_price - 50, current_price + 50)
        
        # Calculate analytics
        gamma_charm = self.analytics.calculate_gamma_charm_interaction(filtered_data)
        
        # Generate professional chart
        output_path = os.path.join(self.output_dir, f"gamma_charm_interaction_professional_{date}.png")
        self.charts.create_professional_gamma_charm_interaction_chart(gamma_charm, current_price, output_path)
        
        return output_path
    
    def generate_professional_vanna_charm_double_trap_chart(self, date: str,
                                                          current_price: float = 6181) -> str:
        """
        Generate a professional "Vanna-Charm Double Trap" chart.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            current_price (float): Current SPX price (default matches original)
            
        Returns:
            str: Path to the generated chart
        """
        print(f"\n=== Generating Professional Vanna-Charm Double Trap Chart ===")
        
        # Load data
        if not self.loader.loaded:
            self.loader.load_data()
        
        date_data = self.loader.filter_by_date(date)
        filtered_data = self.loader.filter_by_strike_range(
            date_data, current_price - 50, current_price + 50)
        
        # Calculate analytics
        vanna_charm = self.analytics.calculate_vanna_charm_double_trap(
            filtered_data, (current_price - 35, current_price + 35))
        
        # Generate professional chart
        output_path = os.path.join(self.output_dir, f"vanna_charm_double_trap_professional_{date}.png")
        self.charts.create_professional_vanna_charm_double_trap_chart(vanna_charm, current_price, output_path)
        
        return output_path
    
    def generate_all_professional_charts(self, date: str,
                                       current_price: float = 6181) -> Dict[str, str]:
        """
        Generate all three professional charts for a given date.
        
        Args:
            date (str): Analysis date in 'YYYY-MM-DD' format
            current_price (float): Current SPX price (default matches original)
            
        Returns:
            Dict[str, str]: Dictionary mapping chart names to file paths
        """
        print(f"\n{'='*80}")
        print(f"GENERATING PROFESSIONAL HIGH-QUALITY CHARTS FOR {date}")
        print(f"Using SPX price: {current_price} (matches original charts)")
        print(f"{'='*80}")
        
        results = {}
        
        try:
            # Generate all three professional charts
            results['charm_bomb'] = self.generate_professional_charm_bomb_chart(date, current_price)
            results['gamma_charm_interaction'] = self.generate_professional_gamma_charm_interaction_chart(date, current_price)
            results['vanna_charm_double_trap'] = self.generate_professional_vanna_charm_double_trap_chart(date, current_price)
            
            print(f"\n{'='*80}")
            print(f"ALL PROFESSIONAL CHARTS GENERATED SUCCESSFULLY!")
            print(f"High-quality, polished charts with professional styling")
            print(f"{'='*80}")
            
            for chart_name, path in results.items():
                print(f"{chart_name}: {path}")
            
            return results
            
        except Exception as e:
            print(f"Error generating professional charts: {e}")
            raise


def main():
    """
    Main function to run the Professional SPX Options Analytics System.
    """
    parser = argparse.ArgumentParser(
        description="Professional SPX Options Analytics System - Generate high-quality charts",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Generate all professional charts
  python professional_main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01
  
  # Generate specific professional chart
  python professional_main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --chart charm_bomb
  
  # Use custom SPX price
  python professional_main.py --csv_path spx_complete_2025_q2.csv --date 2025-04-01 --price 6200
        """)
    
    parser.add_argument('--csv_path', required=True, 
                       help='Path to the SPX options CSV file')
    parser.add_argument('--date', required=True,
                       help='Analysis date in YYYY-MM-DD format')
    parser.add_argument('--chart', choices=['charm_bomb', 'gamma_charm_interaction', 'vanna_charm_double_trap', 'all'],
                       default='all', help='Which chart(s) to generate (default: all)')
    parser.add_argument('--output_dir', default='professional_output',
                       help='Output directory for charts (default: professional_output)')
    parser.add_argument('--price', type=float, default=6181,
                       help='SPX price to use (default: 6181, matches originals)')
    
    args = parser.parse_args()
    
    # Validate CSV file exists
    if not os.path.exists(args.csv_path):
        print(f"Error: CSV file not found: {args.csv_path}")
        sys.exit(1)
    
    # Validate date format
    try:
        datetime.strptime(args.date, '%Y-%m-%d')
    except ValueError:
        print(f"Error: Invalid date format. Use YYYY-MM-DD format")
        sys.exit(1)
    
    # Initialize the professional analytics system
    try:
        analytics_system = ProfessionalSPXOptionsAnalytics(args.csv_path, args.output_dir)
    except Exception as e:
        print(f"Error initializing professional analytics system: {e}")
        sys.exit(1)
    
    # Generate professional charts
    try:
        if args.chart == 'all':
            results = analytics_system.generate_all_professional_charts(args.date, args.price)
        elif args.chart == 'charm_bomb':
            path = analytics_system.generate_professional_charm_bomb_chart(args.date, args.price)
            results = {'charm_bomb': path}
        elif args.chart == 'gamma_charm_interaction':
            path = analytics_system.generate_professional_gamma_charm_interaction_chart(args.date, args.price)
            results = {'gamma_charm_interaction': path}
        elif args.chart == 'vanna_charm_double_trap':
            path = analytics_system.generate_professional_vanna_charm_double_trap_chart(args.date, args.price)
            results = {'vanna_charm_double_trap': path}
        
        print(f"\nProfessional chart generation completed successfully!")
        print(f"Charts feature high-quality styling and professional appearance.")
        print(f"\nOutput files:")
        for chart_name, path in results.items():
            print(f"  {chart_name}: {path}")
            
    except Exception as e:
        print(f"Error generating professional charts: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()


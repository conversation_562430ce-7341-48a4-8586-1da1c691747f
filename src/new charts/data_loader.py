"""
Options Data Loader Module

This module provides functionality to load and filter SPX options data
for analytics and chart generation.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Optional, List, Tuple, Dict, Any


class OptionsDataLoader:
    """
    A class to load and filter SPX options data from CSV files.
    
    This class provides methods to load options data, filter by various criteria,
    and prepare data for analytics calculations.
    """
    
    def __init__(self, csv_path: str):
        """
        Initialize the data loader with a CSV file path.
        
        Args:
            csv_path (str): Path to the CSV file containing options data
        """
        self.csv_path = csv_path
        self.data = None
        self.loaded = False
        
    def load_data(self) -> pd.DataFrame:
        """
        Load the options data from CSV file.
        
        Returns:
            pd.DataFrame: Loaded options data
        """
        try:
            print("Loading options data from CSV...")
            self.data = pd.read_csv(self.csv_path)
            
            # Convert date columns to datetime
            self.data['date'] = pd.to_datetime(self.data['date'])
            self.data['Expiry Date'] = pd.to_datetime(self.data['Expiry Date'])
            
            # Calculate days to expiry
            self.data['DTE'] = (self.data['Expiry Date'] - self.data['date']).dt.days
            
            # Calculate moneyness (Strike / Spot)
            self.data['Moneyness'] = self.data['Strike'] / self.data['spx_close']
            
            # Add time to expiry in years for Greeks calculations
            self.data['T'] = self.data['DTE'] / 365.0
            
            self.loaded = True
            print(f"Loaded {len(self.data)} options records")
            print(f"Date range: {self.data['date'].min()} to {self.data['date'].max()}")
            print(f"Strike range: {self.data['Strike'].min()} to {self.data['Strike'].max()}")
            
            return self.data
            
        except Exception as e:
            print(f"Error loading data: {e}")
            raise
    
    def filter_by_date(self, target_date: str) -> pd.DataFrame:
        """
        Filter data for a specific date.
        
        Args:
            target_date (str): Target date in 'YYYY-MM-DD' format
            
        Returns:
            pd.DataFrame: Filtered data for the target date
        """
        if not self.loaded:
            self.load_data()
            
        target_dt = pd.to_datetime(target_date)
        filtered_data = self.data[self.data['date'] == target_dt].copy()
        
        print(f"Filtered to {len(filtered_data)} records for date {target_date}")
        return filtered_data
    
    def filter_by_expiry(self, data: pd.DataFrame, expiry_date: str) -> pd.DataFrame:
        """
        Filter data for a specific expiry date.
        
        Args:
            data (pd.DataFrame): Input data to filter
            expiry_date (str): Expiry date in 'YYYY-MM-DD' format
            
        Returns:
            pd.DataFrame: Filtered data for the expiry date
        """
        expiry_dt = pd.to_datetime(expiry_date)
        filtered_data = data[data['Expiry Date'] == expiry_dt].copy()
        
        print(f"Filtered to {len(filtered_data)} records for expiry {expiry_date}")
        return filtered_data
    
    def filter_by_dte_range(self, data: pd.DataFrame, min_dte: int, max_dte: int) -> pd.DataFrame:
        """
        Filter data by days to expiry range.
        
        Args:
            data (pd.DataFrame): Input data to filter
            min_dte (int): Minimum days to expiry
            max_dte (int): Maximum days to expiry
            
        Returns:
            pd.DataFrame: Filtered data within DTE range
        """
        filtered_data = data[(data['DTE'] >= min_dte) & (data['DTE'] <= max_dte)].copy()
        
        print(f"Filtered to {len(filtered_data)} records with DTE between {min_dte} and {max_dte}")
        return filtered_data
    
    def filter_by_strike_range(self, data: pd.DataFrame, min_strike: float, max_strike: float) -> pd.DataFrame:
        """
        Filter data by strike price range.
        
        Args:
            data (pd.DataFrame): Input data to filter
            min_strike (float): Minimum strike price
            max_strike (float): Maximum strike price
            
        Returns:
            pd.DataFrame: Filtered data within strike range
        """
        filtered_data = data[(data['Strike'] >= min_strike) & (data['Strike'] <= max_strike)].copy()
        
        print(f"Filtered to {len(filtered_data)} records with strikes between {min_strike} and {max_strike}")
        return filtered_data
    
    def filter_by_moneyness_range(self, data: pd.DataFrame, min_moneyness: float, max_moneyness: float) -> pd.DataFrame:
        """
        Filter data by moneyness range.
        
        Args:
            data (pd.DataFrame): Input data to filter
            min_moneyness (float): Minimum moneyness (Strike/Spot)
            max_moneyness (float): Maximum moneyness (Strike/Spot)
            
        Returns:
            pd.DataFrame: Filtered data within moneyness range
        """
        filtered_data = data[(data['Moneyness'] >= min_moneyness) & (data['Moneyness'] <= max_moneyness)].copy()
        
        print(f"Filtered to {len(filtered_data)} records with moneyness between {min_moneyness:.3f} and {max_moneyness:.3f}")
        return filtered_data
    
    def filter_by_option_type(self, data: pd.DataFrame, option_type: str) -> pd.DataFrame:
        """
        Filter data by option type (calls or puts).
        
        Args:
            data (pd.DataFrame): Input data to filter
            option_type (str): 'c' for calls, 'p' for puts
            
        Returns:
            pd.DataFrame: Filtered data for the option type
        """
        filtered_data = data[data['Call/Put'] == option_type].copy()
        
        option_name = "calls" if option_type == 'c' else "puts"
        print(f"Filtered to {len(filtered_data)} {option_name}")
        return filtered_data
    
    def get_unique_dates(self) -> List[str]:
        """
        Get list of unique trading dates in the dataset.
        
        Returns:
            List[str]: List of unique dates
        """
        if not self.loaded:
            self.load_data()
            
        unique_dates = sorted(self.data['date'].dt.strftime('%Y-%m-%d').unique())
        return unique_dates
    
    def get_unique_expiries(self, data: Optional[pd.DataFrame] = None) -> List[str]:
        """
        Get list of unique expiry dates in the dataset.
        
        Args:
            data (pd.DataFrame, optional): Data to analyze. If None, uses full dataset.
            
        Returns:
            List[str]: List of unique expiry dates
        """
        if data is None:
            if not self.loaded:
                self.load_data()
            data = self.data
            
        unique_expiries = sorted(data['Expiry Date'].dt.strftime('%Y-%m-%d').unique())
        return unique_expiries
    
    def get_data_summary(self) -> Dict[str, Any]:
        """
        Get summary statistics of the loaded data.
        
        Returns:
            Dict[str, Any]: Summary statistics
        """
        if not self.loaded:
            self.load_data()
            
        summary = {
            'total_records': len(self.data),
            'date_range': (self.data['date'].min().strftime('%Y-%m-%d'), 
                          self.data['date'].max().strftime('%Y-%m-%d')),
            'strike_range': (self.data['Strike'].min(), self.data['Strike'].max()),
            'unique_dates': len(self.data['date'].unique()),
            'unique_expiries': len(self.data['Expiry Date'].unique()),
            'calls_count': len(self.data[self.data['Call/Put'] == 'c']),
            'puts_count': len(self.data[self.data['Call/Put'] == 'p']),
            'dte_range': (self.data['DTE'].min(), self.data['DTE'].max())
        }
        
        return summary


def main():
    """
    Example usage of the OptionsDataLoader class.
    """
    # Initialize the data loader
    loader = OptionsDataLoader('/home/<USER>/upload/spx_complete_2025_q2.csv')
    
    # Load and summarize data
    data = loader.load_data()
    summary = loader.get_data_summary()
    
    print("\nData Summary:")
    for key, value in summary.items():
        print(f"{key}: {value}")
    
    # Example filtering
    print("\nExample filtering:")
    
    # Filter for a specific date
    date_data = loader.filter_by_date('2025-04-01')
    
    # Filter for calls only
    calls_data = loader.filter_by_option_type(date_data, 'c')
    
    # Filter by strike range around current price
    if len(calls_data) > 0:
        current_price = calls_data['spx_close'].iloc[0]
        strike_filtered = loader.filter_by_strike_range(calls_data, 
                                                       current_price - 200, 
                                                       current_price + 200)
        print(f"Current SPX price: {current_price}")
        print(f"Strikes around current price: {len(strike_filtered)} options")


if __name__ == "__main__":
    main()


"""
SPX Options Analytics System - Demonstration Script

This script demonstrates the single function calls for generating each chart type.
"""

from data_loader import OptionsDataLoader
from analytics_engine import OptionsAnalyticsEngine
from chart_generator import ChartGenerator
import os

def demo_single_function_calls():
    """
    Demonstrate single function calls for each chart type.
    """
    print("SPX Options Analytics System - Single Function Call Demo")
    print("=" * 60)
    
    # Initialize components
    print("1. Initializing components...")
    loader = OptionsDataLoader('/home/<USER>/upload/spx_complete_2025_q2.csv')
    analytics = OptionsAnalyticsEngine()
    charts = ChartGenerator()
    
    # Load and filter data
    print("2. Loading and filtering data...")
    data = loader.load_data()
    date_data = loader.filter_by_date('2025-04-01')
    current_price = date_data['spx_close'].iloc[0]
    
    # Filter for analysis
    filtered_data = loader.filter_by_strike_range(date_data, 
                                                 current_price - 100, 
                                                 current_price + 100)
    filtered_data = loader.filter_by_dte_range(filtered_data, 1, 30)
    
    print(f"Current SPX price: {current_price:.2f}")
    print(f"Working with {len(filtered_data)} options")
    
    # Create output directory
    os.makedirs('demo_output', exist_ok=True)
    
    print("\n3. Generating charts with single function calls...")
    
    # === SINGLE FUNCTION CALL 1: Charm Profile Chart ===
    print("\n--- Charm Profile Chart ---")
    charm_profile = analytics.calculate_charm_profile(
        filtered_data, (current_price - 50, current_price + 50))
    charm_levels = analytics.get_charm_bomb_levels(current_price, filtered_data)
    
    chart1 = charts.create_charm_profile_chart(
        charm_profile, current_price, charm_levels, 'demo_output/charm_profile_demo.png')
    print("✓ Charm Profile chart generated")
    
    # === SINGLE FUNCTION CALL 2: Gamma-Charm Interaction Chart ===
    print("\n--- Gamma-Charm Interaction Chart ---")
    gamma_charm = analytics.calculate_gamma_charm_interaction(filtered_data)
    
    chart2 = charts.create_gamma_charm_interaction_chart(
        gamma_charm, current_price, 'demo_output/gamma_charm_interaction_demo.png')
    print("✓ Gamma-Charm Interaction chart generated")
    
    # === SINGLE FUNCTION CALL 3: Vanna-Charm Double Trap Chart ===
    print("\n--- Vanna-Charm Double Trap Chart ---")
    vanna_charm = analytics.calculate_vanna_charm_double_trap(
        filtered_data, (current_price - 50, current_price + 50))
    
    chart3 = charts.create_vanna_charm_double_trap_chart(
        vanna_charm, current_price, 'demo_output/vanna_charm_double_trap_demo.png')
    print("✓ Vanna-Charm Double Trap chart generated")
    
    # === SINGLE FUNCTION CALL 4: Summary Dashboard ===
    print("\n--- Summary Dashboard ---")
    dashboard = charts.create_summary_dashboard(
        charm_profile, gamma_charm, vanna_charm, 
        current_price, charm_levels, 'demo_output/dashboard_demo.png')
    print("✓ Summary Dashboard generated")
    
    print("\n" + "=" * 60)
    print("DEMO COMPLETED SUCCESSFULLY!")
    print("=" * 60)
    print("\nGenerated files:")
    print("  - demo_output/charm_profile_demo.png")
    print("  - demo_output/gamma_charm_interaction_demo.png") 
    print("  - demo_output/vanna_charm_double_trap_demo.png")
    print("  - demo_output/dashboard_demo.png")
    
    print("\nEach chart was generated with a single function call!")
    print("The system provides a clean, modular interface for options analytics.")

if __name__ == "__main__":
    demo_single_function_calls()


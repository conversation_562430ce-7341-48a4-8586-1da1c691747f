#!/usr/bin/env python3
"""
Hidden Outliers Analysis Generator - Main Application
====================================================
Orchestrates data management, analytics, visualization, and report generation.
"""

import os
import sys
import argparse
from datetime import datetime
from typing import Optional

from data_manager import DataManager
from analytics import OptionsAnalytics
from visualization import ChartGenerator
from report_generator import ReportGenerator
from config import TickerConfig, list_supported_tickers


class HiddenOutliersAnalyzer:
    """Main application class that orchestrates all analysis components."""
    
    def __init__(self, ticker: str = 'SPX', output_dir: str = "output", use_ai_narrative: bool = True):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.output_dir = output_dir
        self.use_ai_narrative = use_ai_narrative

        # Initialize components with ticker configuration
        self.data_manager = DataManager(ticker=self.ticker)
        self.analytics = OptionsAnalytics(ticker=self.ticker)
        self.chart_generator = ChartGenerator(ticker=self.ticker, output_dir=os.path.join(output_dir, "charts"))
        self.report_generator = ReportGenerator(ticker=self.ticker, output_dir=output_dir, use_ai_narrative=use_ai_narrative)
        
        # Analysis results
        self.data = None
        self.metrics_df = None
        self.vanna_exposure = None
        self.trends = None
        self.anomalies = None
        self.market_regime = None
        self.current_price = None
    
    def load_data(self, data_file: Optional[str] = None, auto_find: bool = False):
        """Load and validate options data."""
        print("Loading options data...")
        self.data = self.data_manager.load_data(data_file, auto_find)
        
        # Get current price from latest data
        latest_data = self.data_manager.get_latest_data()
        self.current_price = latest_data[self.ticker_config.price_column].iloc[0]

        print(f"Data loaded: {len(self.data)} rows")
        print(f"Current {self.ticker}: {self.ticker_config.format_price(self.current_price)}")
    
    def calculate_metrics(self):
        """Calculate all analytics metrics."""
        print("Calculating options metrics...")
        
        # Calculate daily metrics
        self.metrics_df = self.analytics.calculate_daily_metrics(self.data)
        print(f"Calculated metrics for {len(self.metrics_df)} trading days")
        
        # Calculate vanna exposure
        self.vanna_exposure = self.analytics.calculate_vanna_exposure(self.data, self.current_price)
        print(f"Calculated vanna exposure for {len(self.vanna_exposure)} strikes")

        # Calculate forward strikes analysis
        self.forward_strikes = self.analytics.calculate_forward_strikes_analysis(self.data, self.current_price)
        print(f"Calculated forward strikes analysis for {len(self.forward_strikes)} strikes above current price")
        
        # Analyze trends
        self.trends = self.analytics.analyze_trends(self.metrics_df)
        print(f"Trend analysis: {self.trends}")
        
        # Identify anomalies
        self.anomalies = self.analytics.identify_anomalies(self.metrics_df)
        print(f"Identified {len(self.anomalies)} anomalies")
        
        # Determine market regime
        self.market_regime = self.analytics.get_market_regime(self.metrics_df)
        print(f"Market regime: {self.market_regime}")
    
    def generate_charts(self):
        """Generate all visualization charts."""
        print("Generating charts...")
        
        # Generate vanna exposure chart
        self.vanna_chart_path = self.chart_generator.create_vanna_exposure_chart(
            self.vanna_exposure, self.current_price
        )
        print(f"Vanna exposure chart: {self.vanna_chart_path}")
        
        # Generate detailed analysis chart
        self.detailed_chart_path = self.chart_generator.create_detailed_analysis_chart(
            self.metrics_df
        )
        print(f"Detailed analysis chart: {self.detailed_chart_path}")
        
        # Generate summary chart
        self.summary_chart_path = self.chart_generator.create_summary_chart(
            self.metrics_df, self.anomalies
        )
        print(f"Summary chart: {self.summary_chart_path}")

        # Generate charm analysis chart
        self.charm_chart_path = self.chart_generator.create_charm_analysis_chart(
            self.metrics_df, self.forward_strikes
        )
        print(f"Charm analysis chart: {self.charm_chart_path}")
    
    def generate_report(self):
        """Generate comprehensive PDF report."""
        print("Generating PDF report...")
        
        self.pdf_path = self.report_generator.generate_pdf_report(
            metrics_df=self.metrics_df,
            vanna_chart_path=self.vanna_chart_path,
            detailed_chart_path=self.detailed_chart_path,
            summary_chart_path=self.summary_chart_path,
            trends=self.trends,
            anomalies=self.anomalies,
            market_regime=self.market_regime,
            vanna_data=self.vanna_exposure,
            data_source=self.data_manager.data_source,
            forward_strikes_count=len(self.forward_strikes)
        )
        
        return self.pdf_path
    
    def run_full_analysis(self, data_file: Optional[str] = None, auto_find: bool = False):
        """Run complete analysis pipeline."""
        try:
            # Load data
            self.load_data(data_file, auto_find)
            
            # Calculate metrics
            self.calculate_metrics()
            
            # Generate charts
            self.generate_charts()
            
            # Generate report
            pdf_path = self.generate_report()
            
            return {
                'success': True,
                'pdf_path': pdf_path,
                'vanna_chart_path': self.vanna_chart_path,
                'detailed_chart_path': self.detailed_chart_path,
                'summary_chart_path': self.summary_chart_path,
                'charm_chart_path': self.charm_chart_path,
                'metrics': {
                    'total_rows': len(self.data),
                    'trading_days': len(self.metrics_df),
                    'current_price': self.current_price,
                    'anomalies_count': len(self.anomalies),
                    'market_regime': self.market_regime,
                    'trends': self.trends,
                    'forward_strikes_count': len(self.forward_strikes)
                }
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_summary(self):
        """Get analysis summary."""
        if self.data is None or len(self.data) == 0:
            return "No analysis performed yet."
        
        summary = f"""
        {self.ticker} HIDDEN OUTLIERS ANALYSIS SUMMARY
        ================================
        Data Source: {self.data_manager.data_source}
        Total Records: {len(self.data):,}
        Trading Days: {len(self.metrics_df)}
        Current {self.ticker}: {self.ticker_config.format_price(self.current_price)}
        Market Regime: {self.market_regime.replace('_', ' ').title()}
        
        Trends:
        - GEX: {self.trends.get('gex', 'N/A').title()}
        - Volatility: {self.trends.get('volatility', 'N/A').title()}
        - Sentiment: {self.trends.get('sentiment', 'N/A').title()}
        
        Anomalies Detected: {len(self.anomalies)}
        Vanna Exposure Points: {len(self.vanna_exposure)}
        """
        
        return summary


def main():
    """Main entry point for the application."""
    parser = argparse.ArgumentParser(description='Hidden Outliers Analysis Generator')
    parser.add_argument('--data-file', type=str, help='Path to options data CSV file')
    parser.add_argument('--auto-find', action='store_true', 
                       help='Automatically find the most recent data file')
    parser.add_argument('--output-dir', type=str, default='output', 
                       help='Output directory for reports and charts')
    parser.add_argument('--no-ai', action='store_true', 
                       help='Disable AI-generated narratives')
    parser.add_argument('--summary-only', action='store_true',
                       help='Show summary without generating full report')
    parser.add_argument('--ticker', type=str, default='SPX',
                       help=f'Ticker symbol to analyze (supported: {", ".join(list_supported_tickers())})')

    args = parser.parse_args()
    
    # Print header
    print("=" * 60)
    print("HIDDEN OUTLIERS ANALYSIS GENERATOR")
    print("=" * 60)
    print(f"Ticker: {args.ticker}")
    print(f"Output directory: {args.output_dir}")
    print(f"AI narratives: {'Disabled' if args.no_ai else 'Enabled'}")
    print("=" * 60)

    # Initialize analyzer
    analyzer = HiddenOutliersAnalyzer(
        ticker=args.ticker,
        output_dir=args.output_dir,
        use_ai_narrative=not args.no_ai
    )
    
    try:
        # Run analysis
        print("Starting Hidden Outliers Analysis...")
        result = analyzer.run_full_analysis(
            data_file=args.data_file,
            auto_find=args.auto_find
        )
        
        if result['success']:
            print("\nAnalysis complete!")
            
            if args.summary_only:
                print(analyzer.get_summary())
            else:
                # Print results
                print(f"📊 PDF Report: {result['pdf_path']}")
                print(f"📈 Vanna Chart: {result['vanna_chart_path']}")
                print(f"📊 Detailed Chart: {result['detailed_chart_path']}")
                print(f"📈 Summary Chart: {result['summary_chart_path']}")
                print(f"📊 Charm Chart: {result['charm_chart_path']}")
                
                print("\n" + "=" * 60)
                print("ANALYSIS COMPLETE!")
                print("=" * 60)
                print(f"📊 PDF Report: {result['pdf_path']}")
                print(f"📈 Charts Directory: {os.path.dirname(result['vanna_chart_path'])}")
                print("=" * 60)
                
                print("\nNext steps:")
                print("1. Review the PDF report for detailed analysis")
                print("2. Examine the charts for visual patterns")
                print("3. Monitor market conditions for trigger events")
                print("=" * 60)
        
        else:
            print(f"❌ Analysis failed: {result['error']}")
            sys.exit(1)
    
    except KeyboardInterrupt:
        print("\n⚠️ Analysis interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

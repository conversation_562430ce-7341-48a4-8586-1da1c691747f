#!/usr/bin/env python3
"""
Constants Module
===============
Centralized constants for the Hidden Outliers Analysis Generator.
All magic numbers and configuration values are defined here for maintainability.
"""

# ============================================================================
# ANALYSIS PARAMETERS
# ============================================================================

# Days to Expiration (DTE) Configuration
DTE_MIN = 0                    # Minimum DTE for forward-looking analysis
DTE_MAX = 30                   # Maximum DTE for forward-looking analysis
DTE_FORWARD_MIN = 20           # Minimum DTE for forward expiry analysis
DTE_FORWARD_MAX = 30           # Maximum DTE for forward expiry analysis

# DTE Buckets for forward-looking metrics
DTE_BUCKETS = [
    (0, 7),     # 0-7 days
    (8, 14),    # 8-14 days  
    (15, 21),   # 15-21 days
    (22, 30)    # 22-30 days
]

# Time Conversion
DAYS_PER_YEAR = 365.0          # For converting DTE to years in Black-Scholes

# ============================================================================
# CALCULATION CONSTANTS
# ============================================================================

# Greeks and Exposure Calculations
VOMMA_MULTIPLIER = 0.1         # Simplified vomma calculation multiplier
CONTRACT_MULTIPLIER = 100      # Standard options contract multiplier
NOTIONAL_DIVISOR = 1_000_000   # Convert notional to millions for display

# Statistical Analysis
ROLLING_WINDOW_SIZE = 5        # Rolling window for anomaly detection
Z_SCORE_THRESHOLD = 2.0        # Z-score threshold for anomaly detection
TREND_CHANGE_THRESHOLD = 0.1   # 10% change threshold for trend analysis

# Market Regime Thresholds
HIGH_GEX_THRESHOLD = 100       # GEX level indicating high gamma regime
NEGATIVE_GEX_THRESHOLD = -50   # GEX level indicating negative gamma regime
BEARISH_PC_RATIO = 1.2         # P/C ratio indicating bearish sentiment
NEUTRAL_PC_RATIO = 1.0         # P/C ratio indicating neutral sentiment

# ============================================================================
# VISUALIZATION CONSTANTS
# ============================================================================

# Chart Dimensions
CHART_WIDTH_LARGE = 16         # Width for large charts
CHART_HEIGHT_LARGE = 12        # Height for large charts
CHART_WIDTH_STANDARD = 14      # Width for standard charts
CHART_HEIGHT_STANDARD = 8      # Height for standard charts

# Chart Styling
LINE_WIDTH_THICK = 2.5         # Thick line width for main data
LINE_WIDTH_STANDARD = 2.0      # Standard line width
LINE_WIDTH_THIN = 1.5          # Thin line width for secondary data
ALPHA_FILL = 0.3               # Alpha for fill areas
ALPHA_LINE = 0.8               # Alpha for lines
ALPHA_GRID = 0.3               # Alpha for grid lines
BAR_WIDTH = 2.0                # Width for bar charts

# Chart Colors (using hex codes for consistency)
COLOR_PRIMARY = '#E74C3C'      # Red for primary data (GEX, etc.)
COLOR_SECONDARY = '#2E86AB'    # Blue for secondary data
COLOR_POSITIVE = '#2E8B57'     # Green for positive values
COLOR_NEGATIVE = '#DC143C'     # Dark red for negative values
COLOR_CURRENT_PRICE = '#FF8C00' # Orange for current price lines
COLOR_REFERENCE = '#2C3E50'    # Dark blue-gray for reference lines
COLOR_THETA = '#9B59B6'        # Purple for theta
COLOR_VEGA = '#F39C12'         # Orange for vega
COLOR_CHARM = '#8E44AD'        # Purple for charm

# Font Sizes
FONT_SIZE_TITLE = 18           # Main title font size
FONT_SIZE_SUBTITLE = 14        # Subtitle font size
FONT_SIZE_LABEL = 12           # Axis label font size
FONT_SIZE_LEGEND = 10          # Legend font size

# ============================================================================
# OUTPUT CONSTANTS
# ============================================================================

# File Extensions and Formats
CHART_FORMAT = 'png'           # Chart file format
CHART_DPI = 300                # Chart resolution
PDF_FORMAT = 'pdf'             # Report file format

# Directory Names
CHARTS_DIR = 'charts'          # Charts subdirectory name
OUTPUT_DIR = 'output'          # Default output directory name

# File Names
VANNA_CHART_NAME = 'vanna_exposure_profile.png'
DETAILED_CHART_NAME = 'detailed_analysis.png'
SUMMARY_CHART_NAME = 'summary_insights.png'
CHARM_CHART_NAME = 'charm_analysis.png'
PDF_REPORT_NAME = 'Hidden_Outliers_Analysis.pdf'

# ============================================================================
# DATA VALIDATION CONSTANTS
# ============================================================================

# Minimum data requirements
MIN_DATA_ROWS = 100            # Minimum rows required for analysis
MIN_TRADING_DAYS = 5           # Minimum trading days for trend analysis
MIN_OPEN_INTEREST = 1          # Minimum open interest to include option

# Data quality thresholds
MAX_STRIKE_DEVIATION = 5.0     # Maximum allowed strike price deviation (multiplier)
MIN_STRIKE_VALUE = 1.0         # Minimum strike price value

# ============================================================================
# ERROR HANDLING CONSTANTS
# ============================================================================

# Timeout values (in seconds)
DATA_LOAD_TIMEOUT = 300        # 5 minutes for data loading
CHART_GENERATION_TIMEOUT = 120 # 2 minutes for chart generation
PDF_GENERATION_TIMEOUT = 180   # 3 minutes for PDF generation

# Retry attempts
MAX_RETRY_ATTEMPTS = 3         # Maximum retry attempts for operations
RETRY_DELAY = 1.0              # Delay between retries (seconds)

# ============================================================================
# DISPLAY FORMATTING CONSTANTS
# ============================================================================

# Number formatting
DECIMAL_PLACES_PRICE = 2       # Decimal places for price display
DECIMAL_PLACES_RATIO = 2       # Decimal places for ratios
DECIMAL_PLACES_GREEK = 4       # Decimal places for Greeks

# Progress indicators
PROGRESS_BAR_WIDTH = 60        # Width of progress indicators
SEPARATOR_LENGTH = 60          # Length of separator lines

# ============================================================================
# AI NARRATIVE CONSTANTS
# ============================================================================

# OpenAI API Configuration
MAX_TOKENS_EXECUTIVE = 500     # Max tokens for executive summary
MAX_TOKENS_TECHNICAL = 800     # Max tokens for technical analysis
TEMPERATURE = 0.7              # Temperature for AI generation
MODEL_NAME = 'gpt-4'           # Default OpenAI model

# Content limits
MAX_PROMPT_LENGTH = 4000       # Maximum prompt length
MAX_CONTEXT_LINES = 10         # Maximum context lines in prompts

#!/usr/bin/env python3
"""
Report Generation Module
=======================
Handles PDF creation and AI narrative integration for options analysis reports.
"""

import pandas as pd
import numpy as np
import os
import json
import hashlib
from datetime import datetime, timed<PERSON>ta
from typing import Optional, Dict, List
from dotenv import load_dotenv
import openai

from config import TickerConfig
from constants import (
    OUTPUT_DIR, CHARTS_DIR, PDF_REPORT_NAME,
    MAX_TOKENS_EXECUTIVE, MAX_TOKENS_TECHNICAL, TEMPERATURE, MODEL_NAME,
    PDF_<PERSON>RGIN, PDF_SPACER_LARGE, PDF_SPACER_MEDIUM, PDF_SPACER_SMALL,
    PDF_FONT_SIZE_TITLE, PDF_FONT_SIZE_HEADING, PDF_FONT_SIZE_NORMAL,
    PDF_IMAGE_WIDTH, PDF_IMAGE_HEIGHT_STANDARD, PDF_IMAGE_HEIGHT_LARGE,
    ENABLE_AI_CACHING, AI_CACHE_DURATION_HOURS
)

from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT


class ReportGenerator:
    """
    Generates comprehensive PDF reports with AI-powered narratives.

    Features cost optimization through:
    - Response caching to avoid duplicate API calls
    - Reduced token limits for cost efficiency
    - Use of cheaper GPT-4o-mini model
    - Optional AI narrative generation
    """

    def __init__(self, ticker: str = 'SPX', output_dir: str = OUTPUT_DIR, use_ai_narrative: bool = True):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.output_dir = output_dir
        self.use_ai_narrative = use_ai_narrative
        self.openai_client = None
        self.cache_dir = os.path.join(output_dir, '.ai_cache')  # Cache directory for AI responses

        os.makedirs(output_dir, exist_ok=True)
        if ENABLE_AI_CACHING:
            os.makedirs(self.cache_dir, exist_ok=True)

        # Initialize OpenAI client if AI narratives are enabled
        if use_ai_narrative:
            self._initialize_openai()
    
    def _initialize_openai(self):
        """Initialize OpenAI client for AI narrative generation with cost optimization."""
        try:
            load_dotenv()
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.openai_client = openai.OpenAI(api_key=api_key)
                print(f"✅ OpenAI client initialized (Model: {MODEL_NAME}, Caching: {ENABLE_AI_CACHING})")
            else:
                print("⚠️ No OpenAI API key found. Using default narratives.")
                self.use_ai_narrative = False
        except Exception as e:
            print(f"⚠️ Error initializing OpenAI: {e}. Using default narratives.")
            self.use_ai_narrative = False

    def _get_cache_key(self, prompt: str, analysis_type: str) -> str:
        """Generate cache key for AI responses based on prompt content and market conditions."""
        # Create a hash of the prompt and key market data to enable intelligent caching
        cache_data = {
            'prompt_hash': hashlib.md5(prompt.encode()).hexdigest()[:16],
            'analysis_type': analysis_type,
            'ticker': self.ticker,
            'model': MODEL_NAME,
            'date': datetime.now().strftime('%Y-%m-%d')  # Cache per day for market data
        }
        return hashlib.md5(json.dumps(cache_data, sort_keys=True).encode()).hexdigest()

    def _get_cached_response(self, cache_key: str) -> Optional[str]:
        """Retrieve cached AI response if available and not expired."""
        if not ENABLE_AI_CACHING:
            return None

        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        if not os.path.exists(cache_file):
            return None

        try:
            with open(cache_file, 'r') as f:
                cache_data = json.load(f)

            # Check if cache is expired
            cache_time = datetime.fromisoformat(cache_data['timestamp'])
            if datetime.now() - cache_time > timedelta(hours=AI_CACHE_DURATION_HOURS):
                os.remove(cache_file)  # Remove expired cache
                return None

            print(f"📋 Using cached AI response (saved ${cache_data.get('estimated_cost', 0.01):.3f})")
            return cache_data['response']

        except Exception as e:
            print(f"⚠️ Error reading cache: {e}")
            return None

    def _save_cached_response(self, cache_key: str, response: str, estimated_cost: float = 0.01):
        """Save AI response to cache for future use."""
        if not ENABLE_AI_CACHING:
            return

        cache_file = os.path.join(self.cache_dir, f"{cache_key}.json")
        cache_data = {
            'response': response,
            'timestamp': datetime.now().isoformat(),
            'estimated_cost': estimated_cost,
            'model': MODEL_NAME,
            'ticker': self.ticker
        }

        try:
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2)
        except Exception as e:
            print(f"⚠️ Error saving cache: {e}")
    
    def generate_ai_narrative(self, metrics_df: pd.DataFrame, trends: Dict,
                            anomalies: List, market_regime: str, forward_strikes_count: int = 0) -> str:
        """
        Generate AI-powered executive summary with cost optimization.
        Uses caching to avoid duplicate API calls for similar market conditions.
        """
        if not self.use_ai_narrative or not self.openai_client:
            return self._get_default_narrative()

        try:
            # Prepare optimized context for AI (reduced prompt length for cost efficiency)
            latest_metrics = metrics_df.iloc[-1] if len(metrics_df) > 0 else {}

            # Create concise prompt to minimize token usage while maintaining quality
            prompt = f"""Write 2-paragraph executive summary for {self.ticker} options:

Current: {self.ticker_config.format_price(latest_metrics.get(self.ticker_config.price_column, 0))}, GEX: {latest_metrics.get('gex', 0):.1f}, P/C: {latest_metrics.get('pc_ratio', 0):.2f}
Trends: {trends.get('gex', 'stable')} GEX, {trends.get('sentiment', 'neutral')} sentiment
Regime: {market_regime.replace('_', ' ')}, Anomalies: {len(anomalies)}, Forward strikes: {forward_strikes_count}

Focus on: 1) Key risk factors 2) Price action outlook. Professional tone, actionable insights."""

            # Check cache first to save costs
            cache_key = self._get_cache_key(prompt, 'executive')
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response

            # Make API call with cost-optimized settings
            response = self.openai_client.chat.completions.create(
                model=MODEL_NAME,  # Using cheaper GPT-4o-mini model
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS_EXECUTIVE,  # Reduced from 500 to 300 tokens
                temperature=TEMPERATURE  # Lower temperature for focused responses
            )

            result = response.choices[0].message.content.strip()

            # Cache the response to avoid future API calls
            estimated_cost = (len(prompt) + MAX_TOKENS_EXECUTIVE) * 0.000001  # Rough cost estimate
            self._save_cached_response(cache_key, result, estimated_cost)

            return result

        except Exception as e:
            print(f"⚠️ Error generating AI narrative: {e}")
            return self._get_default_narrative()
    
    def generate_ai_detailed_analysis(self, metrics_df: pd.DataFrame, 
                                    vanna_data: pd.DataFrame) -> str:
        """
        Generate AI-powered detailed analysis with cost optimization.
        Uses caching and reduced token limits to minimize API costs.
        """
        if not self.use_ai_narrative or not self.openai_client:
            return self._get_default_detailed_analysis()

        try:
            # Calculate key statistics for concise prompt
            avg_gex = metrics_df['gex'].mean() if len(metrics_df) > 0 else 0
            max_vanna = vanna_data['vanna'].abs().max() if len(vanna_data) > 0 else 0

            # Optimized prompt for cost efficiency
            prompt = f"""Technical analysis for {self.ticker} options:

Stats: Avg GEX {avg_gex:.1f}, Max Vanna {max_vanna:.4f}, {len(metrics_df)} days, {len(vanna_data)} strikes

Analyze: 1) GEX price impact 2) Vanna exposure risks 3) Trading implications. 3 paragraphs, professional."""

            # Check cache first to save costs
            cache_key = self._get_cache_key(prompt, 'technical')
            cached_response = self._get_cached_response(cache_key)
            if cached_response:
                return cached_response

            # Make API call with cost-optimized settings
            response = self.openai_client.chat.completions.create(
                model=MODEL_NAME,  # Using cheaper GPT-4o-mini model
                messages=[{"role": "user", "content": prompt}],
                max_tokens=MAX_TOKENS_TECHNICAL,  # Reduced from 800 to 500 tokens
                temperature=TEMPERATURE  # Lower temperature for focused responses
            )

            result = response.choices[0].message.content.strip()

            # Cache the response to avoid future API calls
            estimated_cost = (len(prompt) + MAX_TOKENS_TECHNICAL) * 0.000001  # Rough cost estimate
            self._save_cached_response(cache_key, result, estimated_cost)

            return result

        except Exception as e:
            print(f"⚠️ Error generating AI detailed analysis: {e}")
            return self._get_default_detailed_analysis()
    
    def _get_default_narrative(self) -> str:
        """Default narrative when AI is not available."""
        return """
        Current SPX options positioning shows elevated gamma exposure levels, indicating potential for 
        increased volatility suppression around key strike levels. The put/call ratio suggests balanced 
        sentiment with institutional hedging activity remaining within normal ranges.
        
        Market makers' gamma exposure creates natural support and resistance levels that may influence 
        intraday price action. Traders should monitor these levels for potential breakout or reversal 
        signals, particularly during high-volume periods.
        
        Risk management protocols should account for potential gamma squeezes in either direction, 
        with particular attention to options expiration cycles and their impact on underlying volatility.
        """
    
    def _get_default_detailed_analysis(self) -> str:
        """Default detailed analysis when AI is not available."""
        return """
        The gamma exposure profile indicates significant market maker positioning that creates natural 
        price anchoring effects. High positive gamma exposure typically corresponds to lower realized 
        volatility as dealers hedge their positions, while negative gamma can amplify price movements.
        
        Vanna exposure analysis reveals the sensitivity of option deltas to volatility changes, providing 
        insights into how volatility shifts may impact dealer hedging flows. Concentrated vanna exposure 
        at specific strikes suggests these levels may act as magnets during volatility expansion or 
        contraction phases.
        
        The options flow patterns demonstrate institutional positioning preferences, with elevated put 
        activity potentially indicating hedging demand rather than outright bearish sentiment. This 
        distinction is crucial for interpreting market signals correctly.
        
        From a risk management perspective, the current positioning suggests monitoring key gamma and 
        vanna levels for potential inflection points, particularly around major options expiration dates 
        when dealer rebalancing activity typically increases.
        """
    
    def generate_pdf_report(self, metrics_df: pd.DataFrame, vanna_chart_path: str,
                          detailed_chart_path: str, summary_chart_path: str,
                          charm_chart_path: str, trends: Dict, anomalies: List,
                          market_regime: str, vanna_data: pd.DataFrame, data_source: str,
                          forward_strikes_count: int = 0) -> str:
        """Generate comprehensive PDF report."""
        output_path = os.path.join(self.output_dir, "Hidden_Outliers_Analysis.pdf")
        
        # Create PDF document using constants
        doc = SimpleDocTemplate(output_path, pagesize=A4, topMargin=PDF_MARGIN*inch,
                              bottomMargin=PDF_MARGIN*inch, leftMargin=PDF_MARGIN*inch, rightMargin=PDF_MARGIN*inch)
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=PDF_FONT_SIZE_TITLE,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2C3E50')
        )

        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=PDF_FONT_SIZE_HEADING,
            spaceAfter=12,
            textColor=colors.HexColor('#34495E')
        )

        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=PDF_FONT_SIZE_NORMAL,
            spaceAfter=12,
            alignment=TA_JUSTIFY
        )
        
        # Build story
        story = []
        
        # Title page
        story.append(Paragraph("HIDDEN OUTLIERS ANALYSIS", title_style))
        story.append(Paragraph("SPX Options Flow Analysis Report", styles['Heading3']))
        story.append(Spacer(1, PDF_SPACER_LARGE*inch))

        # Report metadata
        current_date = datetime.now().strftime("%B %d, %Y")
        story.append(Paragraph(f"Generated: {current_date}", normal_style))
        story.append(Paragraph(f"Data Source: {data_source}", normal_style))
        story.append(Paragraph(f"Market Regime: {market_regime.replace('_', ' ').title()}", normal_style))
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))
        
        # Executive Summary
        story.append(Paragraph("EXECUTIVE SUMMARY", heading_style))
        ai_narrative = self.generate_ai_narrative(metrics_df, trends, anomalies, market_regime, forward_strikes_count)
        story.append(Paragraph(ai_narrative, normal_style))
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))

        story.append(PageBreak())

        # Vanna Exposure Chart
        story.append(Paragraph("VANNA EXPOSURE PROFILE", heading_style))
        story.append(Paragraph("This chart shows vanna exposure across strike prices for 0-30 DTE options. "
                              "Positive values (green) indicate call vanna exposure, while negative values (red) "
                              "show put vanna exposure. The vertical line shows current SPX price.", normal_style))
        story.append(Spacer(1, PDF_SPACER_SMALL*inch))

        if os.path.exists(vanna_chart_path):
            img = Image(vanna_chart_path, width=PDF_IMAGE_WIDTH*inch, height=PDF_IMAGE_HEIGHT_STANDARD*inch)
            story.append(img)
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))
        
        story.append(PageBreak())
        
        # Detailed Analysis
        story.append(Paragraph("DETAILED TECHNICAL ANALYSIS", heading_style))
        ai_detailed = self.generate_ai_detailed_analysis(metrics_df, vanna_data)
        story.append(Paragraph(ai_detailed, normal_style))
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))

        # Detailed Charts
        if os.path.exists(detailed_chart_path):
            img = Image(detailed_chart_path, width=PDF_IMAGE_WIDTH*inch, height=PDF_IMAGE_HEIGHT_LARGE*inch)
            story.append(img)
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))

        story.append(PageBreak())

        # Charm Analysis
        story.append(Paragraph("CHARM ANALYSIS - TIME DECAY EFFECTS", heading_style))
        story.append(Paragraph("This analysis shows charm (delta time decay) effects across the forward-looking "
                              "timeline (0-30 days). The top chart shows portfolio charm over time, while the bottom "
                              "chart displays charm exposure by expiry date for future price projection insights.", normal_style))
        story.append(Spacer(1, PDF_SPACER_SMALL*inch))

        if os.path.exists(charm_chart_path):
            img = Image(charm_chart_path, width=PDF_IMAGE_WIDTH*inch, height=PDF_IMAGE_HEIGHT_LARGE*inch)
            story.append(img)
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))

        story.append(PageBreak())
        
        # Summary and Anomalies
        story.append(Paragraph("MARKET OVERVIEW & ANOMALIES", heading_style))
        if anomalies:
            story.append(Paragraph(f"Detected {len(anomalies)} anomalies in the options flow data:", normal_style))
            for anomaly in anomalies[:5]:  # Show top 5 anomalies
                story.append(Paragraph(f"• {anomaly['description']} (Z-score: {anomaly['z_score']:.2f})", normal_style))
        else:
            story.append(Paragraph("No significant anomalies detected in the current analysis period.", normal_style))
        
        story.append(Spacer(1, PDF_SPACER_MEDIUM*inch))

        if os.path.exists(summary_chart_path):
            img = Image(summary_chart_path, width=PDF_IMAGE_WIDTH*inch, height=PDF_IMAGE_HEIGHT_STANDARD*inch)
            story.append(img)
        
        # Build PDF
        doc.build(story)
        print(f"PDF report generated: {output_path}")
        
        return output_path

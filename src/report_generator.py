#!/usr/bin/env python3
"""
Report Generation Module
=======================
Handles PDF creation and AI narrative integration for options analysis reports.
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime
from typing import Optional, Dict, List
from dotenv import load_dotenv
import openai

from config import TickerConfig

from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, PageBreak, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib import colors
from reportlab.lib.enums import TA_CENTER, TA_JUSTIFY, TA_LEFT


class ReportGenerator:
    """Generates comprehensive PDF reports with AI-powered narratives."""

    def __init__(self, ticker: str = 'SPX', output_dir: str = "output", use_ai_narrative: bool = True):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.output_dir = output_dir
        self.use_ai_narrative = use_ai_narrative
        self.openai_client = None
        
        os.makedirs(output_dir, exist_ok=True)
        
        # Initialize OpenAI client if AI narratives are enabled
        if use_ai_narrative:
            self._initialize_openai()
    
    def _initialize_openai(self):
        """Initialize OpenAI client for AI narrative generation."""
        try:
            load_dotenv()
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.openai_client = openai.OpenAI(api_key=api_key)
                print("✅ OpenAI client initialized for AI-generated narratives")
            else:
                print("⚠️ No OpenAI API key found. Using default narratives.")
                self.use_ai_narrative = False
        except Exception as e:
            print(f"⚠️ Error initializing OpenAI: {e}. Using default narratives.")
            self.use_ai_narrative = False
    
    def generate_ai_narrative(self, metrics_df: pd.DataFrame, trends: Dict, 
                            anomalies: List, market_regime: str) -> str:
        """Generate AI-powered executive summary."""
        if not self.use_ai_narrative or not self.openai_client:
            return self._get_default_narrative()
        
        try:
            # Prepare context for AI
            latest_metrics = metrics_df.iloc[-1] if len(metrics_df) > 0 else {}
            
            prompt = f"""
            As a professional options flow analyst, write a concise executive summary based on this {self.ticker} options data:

            Latest Metrics:
            - {self.ticker} Close: {self.ticker_config.format_price(latest_metrics.get(self.ticker_config.price_column, 0))}
            - GEX: {latest_metrics.get('gex', 'N/A'):.1f}
            - Put/Call Ratio: {latest_metrics.get('pc_ratio', 'N/A'):.2f}
            - Total Open Interest: {latest_metrics.get('total_oi', 'N/A'):,.0f}
            
            Market Trends: {trends}
            Market Regime: {market_regime}
            Anomalies Detected: {len(anomalies)}
            
            Write a professional 2-3 paragraph summary focusing on:
            1. Current market positioning and gamma exposure levels
            2. Key risks and opportunities based on options flow
            3. Actionable insights for traders and risk managers
            
            Keep it concise, professional, and focused on practical implications.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=500,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"⚠️ Error generating AI narrative: {e}")
            return self._get_default_narrative()
    
    def generate_ai_detailed_analysis(self, metrics_df: pd.DataFrame, 
                                    vanna_data: pd.DataFrame) -> str:
        """Generate AI-powered detailed analysis."""
        if not self.use_ai_narrative or not self.openai_client:
            return self._get_default_detailed_analysis()
        
        try:
            # Calculate key statistics
            avg_gex = metrics_df['gex'].mean() if len(metrics_df) > 0 else 0
            max_vanna = vanna_data['vanna'].abs().max() if len(vanna_data) > 0 else 0
            
            prompt = f"""
            As a quantitative analyst, provide a detailed technical analysis of this SPX options data:
            
            Key Statistics:
            - Average GEX: {avg_gex:.1f}
            - Max Vanna Exposure: {max_vanna:.4f}
            - Data Period: {len(metrics_df)} trading days
            - Vanna Profile Points: {len(vanna_data)}
            
            Provide analysis covering:
            1. Gamma exposure implications for price action
            2. Vanna exposure profile interpretation
            3. Options flow patterns and market structure
            4. Risk management considerations
            
            Write 3-4 paragraphs with technical depth suitable for professional traders.
            """
            
            response = self.openai_client.chat.completions.create(
                model="gpt-4",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=800,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            print(f"⚠️ Error generating AI detailed analysis: {e}")
            return self._get_default_detailed_analysis()
    
    def _get_default_narrative(self) -> str:
        """Default narrative when AI is not available."""
        return """
        Current SPX options positioning shows elevated gamma exposure levels, indicating potential for 
        increased volatility suppression around key strike levels. The put/call ratio suggests balanced 
        sentiment with institutional hedging activity remaining within normal ranges.
        
        Market makers' gamma exposure creates natural support and resistance levels that may influence 
        intraday price action. Traders should monitor these levels for potential breakout or reversal 
        signals, particularly during high-volume periods.
        
        Risk management protocols should account for potential gamma squeezes in either direction, 
        with particular attention to options expiration cycles and their impact on underlying volatility.
        """
    
    def _get_default_detailed_analysis(self) -> str:
        """Default detailed analysis when AI is not available."""
        return """
        The gamma exposure profile indicates significant market maker positioning that creates natural 
        price anchoring effects. High positive gamma exposure typically corresponds to lower realized 
        volatility as dealers hedge their positions, while negative gamma can amplify price movements.
        
        Vanna exposure analysis reveals the sensitivity of option deltas to volatility changes, providing 
        insights into how volatility shifts may impact dealer hedging flows. Concentrated vanna exposure 
        at specific strikes suggests these levels may act as magnets during volatility expansion or 
        contraction phases.
        
        The options flow patterns demonstrate institutional positioning preferences, with elevated put 
        activity potentially indicating hedging demand rather than outright bearish sentiment. This 
        distinction is crucial for interpreting market signals correctly.
        
        From a risk management perspective, the current positioning suggests monitoring key gamma and 
        vanna levels for potential inflection points, particularly around major options expiration dates 
        when dealer rebalancing activity typically increases.
        """
    
    def generate_pdf_report(self, metrics_df: pd.DataFrame, vanna_chart_path: str,
                          detailed_chart_path: str, summary_chart_path: str,
                          trends: Dict, anomalies: List, market_regime: str,
                          vanna_data: pd.DataFrame, data_source: str) -> str:
        """Generate comprehensive PDF report."""
        output_path = os.path.join(self.output_dir, "Hidden_Outliers_Analysis.pdf")
        
        # Create PDF document
        doc = SimpleDocTemplate(output_path, pagesize=A4, topMargin=0.5*inch, 
                              bottomMargin=0.5*inch, leftMargin=0.5*inch, rightMargin=0.5*inch)
        
        # Get styles
        styles = getSampleStyleSheet()
        
        # Custom styles
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.HexColor('#2C3E50')
        )
        
        heading_style = ParagraphStyle(
            'CustomHeading',
            parent=styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor('#34495E')
        )
        
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=11,
            spaceAfter=12,
            alignment=TA_JUSTIFY
        )
        
        # Build story
        story = []
        
        # Title page
        story.append(Paragraph("HIDDEN OUTLIERS ANALYSIS", title_style))
        story.append(Paragraph("SPX Options Flow Analysis Report", styles['Heading3']))
        story.append(Spacer(1, 0.5*inch))
        
        # Report metadata
        current_date = datetime.now().strftime("%B %d, %Y")
        story.append(Paragraph(f"Generated: {current_date}", normal_style))
        story.append(Paragraph(f"Data Source: {data_source}", normal_style))
        story.append(Paragraph(f"Market Regime: {market_regime.replace('_', ' ').title()}", normal_style))
        story.append(Spacer(1, 0.3*inch))
        
        # Executive Summary
        story.append(Paragraph("EXECUTIVE SUMMARY", heading_style))
        ai_narrative = self.generate_ai_narrative(metrics_df, trends, anomalies, market_regime)
        story.append(Paragraph(ai_narrative, normal_style))
        story.append(Spacer(1, 0.3*inch))
        
        story.append(PageBreak())
        
        # Vanna Exposure Chart
        story.append(Paragraph("VANNA EXPOSURE PROFILE", heading_style))
        story.append(Paragraph("This chart shows vanna exposure across strike prices for 0-30 DTE options. "
                              "Positive values (green) indicate call vanna exposure, while negative values (red) "
                              "show put vanna exposure. The vertical line shows current SPX price.", normal_style))
        story.append(Spacer(1, 0.2*inch))
        
        if os.path.exists(vanna_chart_path):
            img = Image(vanna_chart_path, width=7.5*inch, height=4.5*inch)
            story.append(img)
        story.append(Spacer(1, 0.3*inch))
        
        story.append(PageBreak())
        
        # Detailed Analysis
        story.append(Paragraph("DETAILED TECHNICAL ANALYSIS", heading_style))
        ai_detailed = self.generate_ai_detailed_analysis(metrics_df, vanna_data)
        story.append(Paragraph(ai_detailed, normal_style))
        story.append(Spacer(1, 0.3*inch))
        
        # Detailed Charts
        if os.path.exists(detailed_chart_path):
            img = Image(detailed_chart_path, width=7.5*inch, height=5.25*inch)
            story.append(img)
        story.append(Spacer(1, 0.3*inch))
        
        story.append(PageBreak())
        
        # Summary and Anomalies
        story.append(Paragraph("MARKET OVERVIEW & ANOMALIES", heading_style))
        if anomalies:
            story.append(Paragraph(f"Detected {len(anomalies)} anomalies in the options flow data:", normal_style))
            for anomaly in anomalies[:5]:  # Show top 5 anomalies
                story.append(Paragraph(f"• {anomaly['description']} (Z-score: {anomaly['z_score']:.2f})", normal_style))
        else:
            story.append(Paragraph("No significant anomalies detected in the current analysis period.", normal_style))
        
        story.append(Spacer(1, 0.3*inch))
        
        if os.path.exists(summary_chart_path):
            img = Image(summary_chart_path, width=7.5*inch, height=4.5*inch)
            story.append(img)
        
        # Build PDF
        doc.build(story)
        print(f"PDF report generated: {output_path}")
        
        return output_path

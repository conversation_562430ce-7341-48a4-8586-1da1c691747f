#!/usr/bin/env python3
"""
Visualization Module
===================
Handles chart generation including vanna exposure profiles and detailed analysis charts.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
from typing import Optional, <PERSON><PERSON>

from config import TickerConfig
from constants import (
    CHART_WIDTH_LARGE, CHART_HEIGHT_LARGE, CHART_WIDTH_STANDARD, CHART_HEIGHT_STANDARD,
    LINE_WIDTH_THICK, LINE_WIDTH_STANDARD, ALPHA_FILL, ALPHA_LINE, ALPHA_GRID, BAR_WIDTH,
    COLOR_PRIMARY, COLOR_SECONDARY, COLOR_POSITIVE, COLOR_NEGATIVE, COLOR_CURRENT_PRICE,
    COLOR_REFERENCE, COLOR_THETA, COLOR_VEGA, COLOR_CHARM, FONT_SIZE_TITLE, FONT_SIZE_SUBTITLE,
    FONT_SIZE_LABEL, NOTIONAL_DIVISOR, CHART_D<PERSON>, CHARTS_DIR, VANNA_CHART_NAME,
    DETAILED_CHART_NAME, SUMMARY_CHART_NAME, CHARM_CHART_NAME,
    CHARM_PROFILE_CHART_NAME, GAMMA_CHARM_INTERACTION_CHART_NAME, VANNA_CHARM_DOUBLE_TRAP_CHART_NAME
)
from advanced_charts import AdvancedChartGenerator


class ChartGenerator:
    """Generates various charts for options analysis."""
    
    def __init__(self, ticker: str = 'SPX', output_dir: str = f"output/{CHARTS_DIR}"):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Initialize advanced chart generator
        self.advanced_charts = AdvancedChartGenerator()

        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
    
    def create_vanna_exposure_chart(self, vanna_data: pd.DataFrame, current_price: float,
                                  output_path: Optional[str] = None) -> str:
        """
        Create vanna exposure profile chart.
        
        Args:
            vanna_data: DataFrame with Strike, vanna, and notional columns
            current_price: Current underlying price
            output_path: Optional custom output path
            
        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, VANNA_CHART_NAME)
        
        if len(vanna_data) == 0:
            # Create empty chart
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, 'No vanna data available', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=16)
            ax.set_title('Vanna Exposure Profile - No Data', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            return output_path
        
        # Create the chart using constants
        fig, ax = plt.subplots(figsize=(CHART_WIDTH_STANDARD, CHART_HEIGHT_STANDARD))

        # Plot vanna exposure as line chart
        strikes = vanna_data['Strike'].values
        vanna_values = vanna_data['vanna'].values
        notional_values = vanna_data['notional'].values

        # Convert notional to millions and apply vanna sign using constant
        notional_millions = notional_values / NOTIONAL_DIVISOR
        signed_notional = np.sign(vanna_values) * notional_millions

        # Plot as line chart using constants
        ax.plot(strikes, signed_notional, color=COLOR_SECONDARY, linewidth=LINE_WIDTH_THICK, alpha=ALPHA_LINE)
        
        # Fill areas above and below zero with different colors using constants
        ax.fill_between(strikes, signed_notional, 0,
                       where=(signed_notional >= 0), color=COLOR_POSITIVE, alpha=ALPHA_FILL, label='Call Vanna')
        ax.fill_between(strikes, signed_notional, 0,
                       where=(signed_notional < 0), color=COLOR_NEGATIVE, alpha=ALPHA_FILL, label='Put Vanna')

        # Add current price line using constants
        ax.axvline(x=current_price, color=COLOR_CURRENT_PRICE, linestyle='--', linewidth=LINE_WIDTH_STANDARD,
                  alpha=ALPHA_LINE, label=f'Current {self.ticker}: {self.ticker_config.format_price(current_price)}')
        
        # Add zero line using constants
        ax.axhline(y=0, color='#000000', linestyle='-', linewidth=1, alpha=0.5)

        # Styling using constants
        ax.grid(True, alpha=ALPHA_GRID, color='#DDDDDD', linestyle='-', linewidth=0.5)
        ax.set_xlabel('Strike Price', fontsize=FONT_SIZE_LABEL, color='#333333', fontweight='normal')
        ax.set_ylabel('Notional (Millions $)', fontsize=FONT_SIZE_LABEL, color='#333333', fontweight='normal')
        ax.tick_params(colors='#666666', labelsize=10)

        # Title and legend using constants
        ax.set_title(f'{self.ticker} Vanna Exposure Profile (0-30 DTE)', fontsize=FONT_SIZE_TITLE, fontweight='bold',
                    color=COLOR_REFERENCE, pad=20)
        ax.legend(loc='upper right', fontsize=11)
        
        # Add annotations for key levels
        max_positive = signed_notional.max() if len(signed_notional) > 0 else 0
        max_negative = signed_notional.min() if len(signed_notional) > 0 else 0
        
        if max_positive > 0:
            max_pos_strike = strikes[signed_notional.argmax()]
            ax.annotate(f'Max Call Vanna\n${max_pos_strike:.0f}\n${max_positive:.1f}M', 
                       xy=(max_pos_strike, max_positive), 
                       xytext=(max_pos_strike + 50, max_positive * 1.1),
                       arrowprops=dict(arrowstyle='->', color='#2E8B57', alpha=0.7),
                       fontsize=9, ha='center', color='#2E8B57')
        
        if max_negative < 0:
            max_neg_strike = strikes[signed_notional.argmin()]
            ax.annotate(f'Max Put Vanna\n${max_neg_strike:.0f}\n${abs(max_negative):.1f}M', 
                       xy=(max_neg_strike, max_negative), 
                       xytext=(max_neg_strike - 50, max_negative * 1.1),
                       arrowprops=dict(arrowstyle='->', color='#DC143C', alpha=0.7),
                       fontsize=9, ha='center', color='#DC143C')
        
        # Set background color
        ax.set_facecolor('#FAFAFA')
        fig.patch.set_facecolor('white')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=CHART_DPI, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return output_path

    def create_charm_analysis_chart(self, metrics_df: pd.DataFrame, forward_expiry_df: pd.DataFrame,
                                  output_path: Optional[str] = None) -> str:
        """
        Create charm analysis chart showing time decay effects and forward expiry perspective.

        Args:
            metrics_df: DataFrame with daily metrics including charm
            forward_expiry_df: DataFrame with forward expiry analysis (future expiration dates)
            output_path: Optional custom output path

        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, "charm_analysis.png")

        # Create subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 12))
        fig.suptitle(f'{self.ticker} Charm Analysis - Time Decay Effects', fontsize=16, fontweight='bold', y=0.95)

        # Top plot: Charm by DTE bucket
        dte_mids = metrics_df['dte_mid'].values
        dte_labels = metrics_df['dte_bucket'].values

        bars1 = ax1.bar(dte_mids, metrics_df['charm'], alpha=0.7, color='#8E44AD',
                       width=2, label='Charm by DTE Bucket')
        ax1.axhline(y=0, color='#000000', linestyle='-', linewidth=1, alpha=0.5)

        ax1.set_title('Portfolio Charm by DTE Bucket (Delta Time Decay)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Charm (Delta/Day)', fontsize=12)
        ax1.set_xlabel('Days to Expiration', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.legend()

        # Add DTE bucket labels
        for i, (dte_mid, label, charm_val) in enumerate(zip(dte_mids, dte_labels, metrics_df['charm'])):
            ax1.text(dte_mid, charm_val + (max(metrics_df['charm']) - min(metrics_df['charm'])) * 0.02,
                    label, ha='center', va='bottom' if charm_val >= 0 else 'top',
                    fontsize=9, fontweight='bold')

        # Bottom plot: Forward expiry charm profile
        if len(forward_expiry_df) > 0:
            # Group by expiry date and sum charm for visualization
            expiry_charm = forward_expiry_df.groupby('Expiry Date').agg({
                'charm': 'sum',
                'dte': 'first',
                'notional': 'sum'
            }).reset_index()

            dte_values = expiry_charm['dte'].values
            charm_values = expiry_charm['charm'].values
            expiry_dates = expiry_charm['Expiry Date'].values

            # Create bar chart for forward expiry charm
            bars = ax2.bar(dte_values, charm_values, alpha=0.7, color='#E67E22',
                          label='Forward Expiry Charm', width=max(dte_values)*0.02)

            # Color bars based on positive/negative charm
            for i, (bar, charm_val) in enumerate(zip(bars, charm_values)):
                if charm_val >= 0:
                    bar.set_color('#27AE60')  # Green for positive charm
                else:
                    bar.set_color('#E74C3C')  # Red for negative charm

            ax2.axhline(y=0, color='#000000', linestyle='-', linewidth=1, alpha=0.5)
            ax2.set_title('Forward Expiry Charm Profile (Future Expiration Dates)', fontsize=14, fontweight='bold')
            ax2.set_xlabel('Days to Expiration', fontsize=12)
            ax2.set_ylabel('Total Charm by Expiry', fontsize=12)
            ax2.grid(True, alpha=0.3)

            # Add DTE and date labels above significant bars
            max_charm = abs(charm_values).max() if len(charm_values) > 0 else 1
            for i, (dte, charm_val, expiry_date) in enumerate(zip(dte_values, charm_values, expiry_dates)):
                if abs(charm_val) > max_charm * 0.1:  # Only label significant values
                    ax2.text(dte, charm_val + (max_charm * 0.02 if charm_val >= 0 else -max_charm * 0.02),
                            f'{dte}d\n{pd.to_datetime(expiry_date).strftime("%m/%d")}',
                            ha='center', va='bottom' if charm_val >= 0 else 'top', fontsize=9,
                            fontweight='bold', color='#2C3E50')
        else:
            ax2.text(0.5, 0.5, 'No forward expiry data available', ha='center', va='center',
                    transform=ax2.transAxes, fontsize=14, color='#7F8C8D')
            ax2.set_title('Forward Expiry Charm Profile - No Data', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        return output_path

    def create_detailed_analysis_chart(self, metrics_df: pd.DataFrame,
                                     output_path: Optional[str] = None) -> str:
        """
        Create detailed analysis chart with forward-looking metrics by DTE buckets.

        Args:
            metrics_df: DataFrame with forward-looking metrics by DTE buckets
            output_path: Optional custom output path

        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, DETAILED_CHART_NAME)

        # Create subplots using constants
        fig, axes = plt.subplots(2, 2, figsize=(CHART_WIDTH_LARGE, CHART_HEIGHT_LARGE))
        fig.suptitle(f'{self.ticker} Forward-Looking Analysis - DTE Buckets (0-30 Days)', fontsize=FONT_SIZE_TITLE, fontweight='bold', y=0.95)

        # Use DTE mid-points for x-axis
        dte_mids = metrics_df['dte_mid'].values
        dte_labels = metrics_df['dte_bucket'].values
        
        # Plot 1: GEX by DTE bucket using constants
        ax1 = axes[0, 0]
        bars1 = ax1.bar(dte_mids, metrics_df['gex'], alpha=ALPHA_FILL, color=COLOR_PRIMARY, width=BAR_WIDTH)
        ax1.set_title('Gamma Exposure by DTE Bucket', fontsize=FONT_SIZE_SUBTITLE, fontweight='bold')
        ax1.set_ylabel('GEX', fontsize=FONT_SIZE_LABEL)
        ax1.set_xlabel('Days to Expiration', fontsize=FONT_SIZE_LABEL)
        ax1.grid(True, alpha=ALPHA_GRID)

        # Add DTE bucket labels
        for i, (dte_mid, label) in enumerate(zip(dte_mids, dte_labels)):
            ax1.text(dte_mid, metrics_df['gex'].iloc[i] + max(metrics_df['gex']) * 0.02,
                    label, ha='center', va='bottom', fontsize=9, fontweight='bold')

        # Plot 2: Put/Call Ratio by DTE bucket using constants
        ax2 = axes[0, 1]
        bars2 = ax2.bar(dte_mids, metrics_df['pc_ratio'], alpha=ALPHA_FILL, color=COLOR_SECONDARY, width=BAR_WIDTH)
        ax2.axhline(y=1.0, color=COLOR_NEGATIVE, linestyle='--', alpha=ALPHA_LINE, label='Neutral (1.0)')
        ax2.set_title('Put/Call Ratio by DTE Bucket', fontsize=FONT_SIZE_SUBTITLE, fontweight='bold')
        ax2.set_ylabel('P/C Ratio', fontsize=FONT_SIZE_LABEL)
        ax2.set_xlabel('Days to Expiration', fontsize=FONT_SIZE_LABEL)
        ax2.grid(True, alpha=ALPHA_GRID)
        ax2.legend()

        # Add DTE bucket labels
        for i, (dte_mid, label) in enumerate(zip(dte_mids, dte_labels)):
            ax2.text(dte_mid, metrics_df['pc_ratio'].iloc[i] + max(metrics_df['pc_ratio']) * 0.02,
                    label, ha='center', va='bottom', fontsize=9, fontweight='bold')
        
        # Plot 3: Theta and Vega by DTE bucket using constants
        ax3 = axes[1, 0]
        ax3_twin = ax3.twinx()

        bars3a = ax3.bar(dte_mids - 0.5, metrics_df['theta'], alpha=ALPHA_FILL, color=COLOR_THETA,
                        width=1, label='Theta')
        bars3b = ax3_twin.bar(dte_mids + 0.5, metrics_df['vega'], alpha=ALPHA_FILL, color=COLOR_VEGA,
                             width=1, label='Vega')

        ax3.set_title('Theta Burn vs Vega Exposure by DTE', fontsize=FONT_SIZE_SUBTITLE, fontweight='bold')
        ax3.set_ylabel('Theta', fontsize=FONT_SIZE_LABEL, color=COLOR_THETA)
        ax3.set_xlabel('Days to Expiration', fontsize=FONT_SIZE_LABEL)
        ax3_twin.set_ylabel('Vega', fontsize=FONT_SIZE_LABEL, color=COLOR_VEGA)
        ax3.grid(True, alpha=ALPHA_GRID)

        # Add legends
        ax3.legend(loc='upper left')
        ax3_twin.legend(loc='upper right')

        # Plot 4: Charm (Delta Time Decay) by DTE bucket using constants
        ax4 = axes[1, 1]
        bars4 = ax4.bar(dte_mids, metrics_df['charm'], alpha=ALPHA_FILL, color=COLOR_CHARM, width=BAR_WIDTH)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax4.set_title('Charm (Delta Time Decay) by DTE', fontsize=FONT_SIZE_SUBTITLE, fontweight='bold')
        ax4.set_ylabel('Charm', fontsize=FONT_SIZE_LABEL)
        ax4.set_xlabel('Days to Expiration', fontsize=FONT_SIZE_LABEL)
        ax4.grid(True, alpha=ALPHA_GRID)

        # Add DTE bucket labels
        for i, (dte_mid, label) in enumerate(zip(dte_mids, dte_labels)):
            charm_val = metrics_df['charm'].iloc[i]
            ax4.text(dte_mid, charm_val + (max(metrics_df['charm']) - min(metrics_df['charm'])) * 0.02,
                    label, ha='center', va='bottom' if charm_val >= 0 else 'top',
                    fontsize=9, fontweight='bold')
        
        # Adjust layout and save using constants
        plt.tight_layout()
        plt.subplots_adjust(top=0.92)
        plt.savefig(output_path, dpi=CHART_DPI, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return output_path
    
    def create_summary_chart(self, metrics_df: pd.DataFrame, anomalies: list,
                           output_path: Optional[str] = None) -> str:
        """
        Create a forward-looking summary chart highlighting key insights by DTE buckets.

        Args:
            metrics_df: DataFrame with forward-looking metrics by DTE buckets
            anomalies: List of detected anomalies
            output_path: Optional custom output path

        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, SUMMARY_CHART_NAME)

        fig, ax = plt.subplots(figsize=(CHART_WIDTH_STANDARD, CHART_HEIGHT_STANDARD))

        # Use DTE mid-points for x-axis
        dte_mids = metrics_df['dte_mid'].values
        dte_labels = metrics_df['dte_bucket'].values
        current_price = metrics_df[self.ticker_config.price_column].iloc[0]

        # Primary axis: GEX by DTE bucket using constants
        bars = ax.bar(dte_mids, metrics_df['gex'], alpha=ALPHA_FILL, color=COLOR_PRIMARY,
                     width=BAR_WIDTH, label='GEX by DTE Bucket')

        # Add current price as horizontal line for reference using constants
        ax.axhline(y=current_price, color=COLOR_REFERENCE, linestyle='--', linewidth=LINE_WIDTH_STANDARD,
                  alpha=ALPHA_LINE, label=f'{self.ticker} Current: {self.ticker_config.format_price(current_price)}')

        # Add DTE bucket labels on bars
        for i, (dte_mid, label, gex_val) in enumerate(zip(dte_mids, dte_labels, metrics_df['gex'])):
            ax.text(dte_mid, gex_val + max(metrics_df['gex']) * 0.02,
                   label, ha='center', va='bottom', fontsize=10, fontweight='bold')

        # Add secondary axis for Put/Call ratio
        ax2 = ax.twinx()
        line2 = ax2.plot(dte_mids, metrics_df['pc_ratio'], marker='o', linewidth=2.5,
                        color='#3498DB', label='P/C Ratio', markersize=8)
        ax2.axhline(y=1.0, color='blue', linestyle='--', alpha=0.7, label='Neutral P/C (1.0)')

        # Styling
        ax.set_xlabel('Days to Expiration', fontsize=12, fontweight='bold')
        ax.set_ylabel('Gamma Exposure (GEX)', fontsize=12, fontweight='bold', color='#E74C3C')
        ax2.set_ylabel('Put/Call Ratio', fontsize=12, fontweight='bold', color='#3498DB')

        ax.set_title(f'{self.ticker} Forward-Looking Analysis - DTE Buckets (0-30 Days)',
                    fontsize=16, fontweight='bold', pad=20)

        # Grid and formatting
        ax.grid(True, alpha=0.3)

        # Legends
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=CHART_DPI, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return output_path

    def create_charm_profile_chart(self, options_data: pd.DataFrame, current_price: float,
                                 output_path: Optional[str] = None) -> str:
        """
        Create the Charm Profile: Time Decay Battlefield chart.

        Args:
            options_data: DataFrame with options data
            current_price: Current underlying price
            output_path: Optional path to save the chart

        Returns:
            str: Path to the saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, CHARM_PROFILE_CHART_NAME)

        return self.advanced_charts.create_charm_profile_chart(
            options_data, current_price, output_path, self.ticker)

    def create_gamma_charm_interaction_chart(self, options_data: pd.DataFrame, current_price: float,
                                           output_path: Optional[str] = None) -> str:
        """
        Create the Gamma-Charm Interaction scatter plot.

        Args:
            options_data: DataFrame with options data
            current_price: Current underlying price
            output_path: Optional path to save the chart

        Returns:
            str: Path to the saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, GAMMA_CHARM_INTERACTION_CHART_NAME)

        return self.advanced_charts.create_gamma_charm_interaction_chart(
            options_data, current_price, output_path, self.ticker)

    def create_vanna_charm_double_trap_chart(self, options_data: pd.DataFrame, current_price: float,
                                           output_path: Optional[str] = None) -> str:
        """
        Create the Vanna-Charm Double Trap chart.

        Args:
            options_data: DataFrame with options data
            current_price: Current underlying price
            output_path: Optional path to save the chart

        Returns:
            str: Path to the saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, VANNA_CHARM_DOUBLE_TRAP_CHART_NAME)

        return self.advanced_charts.create_vanna_charm_double_trap_chart(
            options_data, current_price, output_path, self.ticker)

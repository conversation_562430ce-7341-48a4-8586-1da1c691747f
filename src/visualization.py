#!/usr/bin/env python3
"""
Visualization Module
===================
Handles chart generation including vanna exposure profiles and detailed analysis charts.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
from typing import Optional, Tuple

from config import TickerConfig


class ChartGenerator:
    """Generates various charts for options analysis."""
    
    def __init__(self, ticker: str = 'SPX', output_dir: str = "output/charts"):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

        # Set style
        plt.style.use('default')
        sns.set_palette("husl")
    
    def create_vanna_exposure_chart(self, vanna_data: pd.DataFrame, current_price: float,
                                  output_path: Optional[str] = None) -> str:
        """
        Create vanna exposure profile chart.
        
        Args:
            vanna_data: DataFrame with Strike, vanna, and notional columns
            current_price: Current underlying price
            output_path: Optional custom output path
            
        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, "vanna_exposure_profile.png")
        
        if len(vanna_data) == 0:
            # Create empty chart
            fig, ax = plt.subplots(figsize=(12, 8))
            ax.text(0.5, 0.5, 'No vanna data available', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=16)
            ax.set_title('Vanna Exposure Profile - No Data', fontsize=16, fontweight='bold')
            plt.tight_layout()
            plt.savefig(output_path, dpi=300, bbox_inches='tight')
            plt.close()
            return output_path
        
        # Create the chart
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # Plot vanna exposure as line chart
        strikes = vanna_data['Strike'].values
        vanna_values = vanna_data['vanna'].values
        notional_values = vanna_data['notional'].values
        
        # Convert notional to millions and apply vanna sign
        notional_millions = notional_values / 1000000  # Convert to millions
        signed_notional = np.sign(vanna_values) * notional_millions
        
        # Plot as line chart
        ax.plot(strikes, signed_notional, color='#2E86AB', linewidth=2.5, alpha=0.9)
        
        # Fill areas above and below zero with different colors
        ax.fill_between(strikes, signed_notional, 0, 
                       where=(signed_notional >= 0), color='#2E8B57', alpha=0.3, label='Call Vanna')
        ax.fill_between(strikes, signed_notional, 0, 
                       where=(signed_notional < 0), color='#DC143C', alpha=0.3, label='Put Vanna')
        
        # Add current price line
        ax.axvline(x=current_price, color='#FF8C00', linestyle='--', linewidth=2,
                  alpha=0.8, label=f'Current {self.ticker}: {self.ticker_config.format_price(current_price)}')
        
        # Add zero line
        ax.axhline(y=0, color='#000000', linestyle='-', linewidth=1, alpha=0.5)
        
        # Styling
        ax.grid(True, alpha=0.3, color='#DDDDDD', linestyle='-', linewidth=0.5)
        ax.set_xlabel('Strike Price', fontsize=12, color='#333333', fontweight='normal')
        ax.set_ylabel('Notional (Millions $)', fontsize=12, color='#333333', fontweight='normal')
        ax.tick_params(colors='#666666', labelsize=10)
        
        # Title and legend
        ax.set_title(f'{self.ticker} Vanna Exposure Profile (0-30 DTE)', fontsize=16, fontweight='bold',
                    color='#2C3E50', pad=20)
        ax.legend(loc='upper right', fontsize=11)
        
        # Add annotations for key levels
        max_positive = signed_notional.max() if len(signed_notional) > 0 else 0
        max_negative = signed_notional.min() if len(signed_notional) > 0 else 0
        
        if max_positive > 0:
            max_pos_strike = strikes[signed_notional.argmax()]
            ax.annotate(f'Max Call Vanna\n${max_pos_strike:.0f}\n${max_positive:.1f}M', 
                       xy=(max_pos_strike, max_positive), 
                       xytext=(max_pos_strike + 50, max_positive * 1.1),
                       arrowprops=dict(arrowstyle='->', color='#2E8B57', alpha=0.7),
                       fontsize=9, ha='center', color='#2E8B57')
        
        if max_negative < 0:
            max_neg_strike = strikes[signed_notional.argmin()]
            ax.annotate(f'Max Put Vanna\n${max_neg_strike:.0f}\n${abs(max_negative):.1f}M', 
                       xy=(max_neg_strike, max_negative), 
                       xytext=(max_neg_strike - 50, max_negative * 1.1),
                       arrowprops=dict(arrowstyle='->', color='#DC143C', alpha=0.7),
                       fontsize=9, ha='center', color='#DC143C')
        
        # Set background color
        ax.set_facecolor('#FAFAFA')
        fig.patch.set_facecolor('white')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return output_path
    
    def create_detailed_analysis_chart(self, metrics_df: pd.DataFrame, 
                                     output_path: Optional[str] = None) -> str:
        """
        Create detailed analysis chart with multiple subplots.
        
        Args:
            metrics_df: DataFrame with daily metrics
            output_path: Optional custom output path
            
        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, "detailed_analysis.png")
        
        # Create subplots
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Hidden Outliers Analysis - Detailed Metrics', fontsize=18, fontweight='bold', y=0.95)
        
        # Format dates for x-axis
        dates = pd.to_datetime(metrics_df['date'])
        date_labels = [d.strftime('%m/%d') for d in dates]
        
        # Plot 1: GEX over time
        ax1 = axes[0, 0]
        ax1.plot(dates, metrics_df['gex'], marker='o', linewidth=2.5, markersize=6, color='#E74C3C')
        ax1.fill_between(dates, metrics_df['gex'], alpha=0.3, color='#E74C3C')
        ax1.set_title('Gamma Exposure (GEX)', fontsize=14, fontweight='bold')
        ax1.set_ylabel('GEX', fontsize=12)
        ax1.grid(True, alpha=0.3)
        ax1.tick_params(axis='x', rotation=45)
        
        # Plot 2: Put/Call Ratio
        ax2 = axes[0, 1]
        ax2.plot(dates, metrics_df['pc_ratio'], marker='s', linewidth=2.5, markersize=6, color='#3498DB')
        ax2.fill_between(dates, metrics_df['pc_ratio'], alpha=0.3, color='#3498DB')
        ax2.axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Neutral (1.0)')
        ax2.set_title('Put/Call Ratio', fontsize=14, fontweight='bold')
        ax2.set_ylabel('P/C Ratio', fontsize=12)
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.tick_params(axis='x', rotation=45)
        
        # Plot 3: Theta and Vega
        ax3 = axes[1, 0]
        ax3_twin = ax3.twinx()
        
        line1 = ax3.plot(dates, metrics_df['theta'], marker='^', linewidth=2.5, markersize=6, 
                        color='#9B59B6', label='Theta')
        line2 = ax3_twin.plot(dates, metrics_df['vega'], marker='v', linewidth=2.5, markersize=6, 
                             color='#F39C12', label='Vega')
        
        ax3.set_title('Theta Burn vs Vega Exposure', fontsize=14, fontweight='bold')
        ax3.set_ylabel('Theta', fontsize=12, color='#9B59B6')
        ax3_twin.set_ylabel('Vega', fontsize=12, color='#F39C12')
        ax3.grid(True, alpha=0.3)
        ax3.tick_params(axis='x', rotation=45)
        
        # Combine legends
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax3.legend(lines, labels, loc='upper left')
        
        # Plot 4: Open Interest
        ax4 = axes[1, 1]
        width = 0.35
        x = np.arange(len(dates))
        
        bars1 = ax4.bar(x - width/2, metrics_df['call_oi'], width, label='Call OI', 
                       color='#2ECC71', alpha=0.8)
        bars2 = ax4.bar(x + width/2, metrics_df['put_oi'], width, label='Put OI', 
                       color='#E67E22', alpha=0.8)
        
        ax4.set_title('Open Interest Distribution', fontsize=14, fontweight='bold')
        ax4.set_ylabel('Open Interest', fontsize=12)
        ax4.set_xticks(x)
        ax4.set_xticklabels(date_labels, rotation=45)
        ax4.legend()
        ax4.grid(True, alpha=0.3, axis='y')
        
        # Adjust layout and save
        plt.tight_layout()
        plt.subplots_adjust(top=0.92)
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return output_path
    
    def create_summary_chart(self, metrics_df: pd.DataFrame, anomalies: list,
                           output_path: Optional[str] = None) -> str:
        """
        Create a summary chart highlighting key insights and anomalies.
        
        Args:
            metrics_df: DataFrame with daily metrics
            anomalies: List of detected anomalies
            output_path: Optional custom output path
            
        Returns:
            Path to saved chart
        """
        if output_path is None:
            output_path = os.path.join(self.output_dir, "summary_insights.png")
        
        fig, ax = plt.subplots(figsize=(14, 8))
        
        # Plot price with GEX overlay
        dates = pd.to_datetime(metrics_df['date'])

        # Primary axis: underlying price
        ax.plot(dates, metrics_df[self.ticker_config.price_column], linewidth=3, color='#2C3E50',
               label=f'{self.ticker} Price', marker='o', markersize=8)
        
        # Secondary axis: GEX
        ax2 = ax.twinx()
        ax2.plot(dates, metrics_df['gex'], linewidth=2.5, color='#E74C3C', 
                label='GEX', marker='s', markersize=6, alpha=0.8)
        
        # Highlight anomalies
        for anomaly in anomalies:
            anomaly_date = pd.to_datetime(anomaly['date'])
            if anomaly['type'] == 'gex_anomaly':
                ax2.scatter(anomaly_date, anomaly['value'], color='red', s=200, 
                           marker='X', zorder=5, label='GEX Anomaly')
        
        # Styling
        ax.set_xlabel('Date', fontsize=12, fontweight='bold')
        ax.set_ylabel(f'{self.ticker} Price', fontsize=12, fontweight='bold', color='#2C3E50')
        ax2.set_ylabel('Gamma Exposure (GEX)', fontsize=12, fontweight='bold', color='#E74C3C')
        
        ax.set_title('Market Overview with Anomaly Detection', fontsize=16, fontweight='bold', pad=20)
        
        # Grid and formatting
        ax.grid(True, alpha=0.3)
        ax.tick_params(axis='x', rotation=45)
        
        # Legends
        lines1, labels1 = ax.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()
        
        return output_path

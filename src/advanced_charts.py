"""
Professional Advanced Chart Generator Module

This module creates high-quality, professional charts for advanced options analysis,
based on the user's professional chart generator with institutional-grade styling.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
from matplotlib.patches import Rectangle, FancyBboxPatch, Circle
import matplotlib.patheffects as path_effects
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import seaborn as sns
import os
from scipy.stats import norm
import warnings
warnings.filterwarnings('ignore')

try:
    from constants import (
        CHARM_PROFILE_CHART_NAME,
        GAMMA_CHARM_INTERACTION_CHART_NAME,
        VANNA_CHARM_DOUBLE_TRAP_CHART_NAME,
        CHARTS_DIR
    )
except ImportError:
    # Fallback constants if running standalone
    CHARM_PROFILE_CHART_NAME = 'charm_profile_battlefield.png'
    GAMMA_CHARM_INTERACTION_CHART_NAME = 'gamma_charm_interaction.png'
    VANNA_CHARM_DOUBLE_TRAP_CHART_NAME = 'vanna_charm_double_trap.png'
    CHARTS_DIR = 'charts'

# Set high DPI for professional charts with good quality
plt.rcParams['figure.dpi'] = 200
plt.rcParams['savefig.dpi'] = 200
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans', 'Liberation Sans']
plt.rcParams['axes.linewidth'] = 0.8
plt.rcParams['grid.linewidth'] = 0.5
plt.rcParams['lines.linewidth'] = 2.5


class AdvancedChartGenerator:
    """
    Professional chart generator for advanced options analytics.
    Creates institutional-grade charts with sophisticated styling.
    """
    
    def __init__(self):
        """Initialize the professional chart generator."""
        # Professional color palette matching originals
        self.colors = {
            'primary_teal': '#1BA3A3',        # Professional teal
            'primary_orange': '#FF8C00',      # Professional orange
            'primary_red': '#E53E3E',         # Professional red
            'primary_blue': '#3182CE',        # Professional blue
            'success_green': '#38A169',       # Success green
            'warning_orange': '#DD6B20',      # Warning orange
            'error_red': '#E53E3E',          # Error red
            'text_primary': '#2D3748',        # Primary text
            'text_secondary': '#4A5568',      # Secondary text
            'text_muted': '#718096',          # Muted text
            'bg_light_green': '#F0FFF4',      # Light green background
            'bg_light_red': '#FED7D7',        # Light red background
            'border_light': '#E2E8F0',       # Light border
            'border_medium': '#CBD5E0',       # Medium border
            'white': '#FFFFFF',               # Pure white
            'gray_50': '#F7FAFC',            # Very light gray
            'gray_100': '#EDF2F7',           # Light gray
            'gray_200': '#E2E8F0'            # Medium light gray
        }
        
        # Professional typography
        self.fonts = {
            'title': {'fontsize': 20, 'weight': 'bold', 'color': self.colors['text_primary']},
            'subtitle': {'fontsize': 14, 'weight': 'normal', 'color': self.colors['error_red']},
            'section_title': {'fontsize': 16, 'weight': 'bold', 'color': self.colors['text_primary']},
            'label': {'fontsize': 12, 'weight': 'normal', 'color': self.colors['text_secondary']},
            'annotation': {'fontsize': 11, 'weight': 'bold'},
            'metric_title': {'fontsize': 10, 'weight': 'normal', 'color': self.colors['text_muted']},
            'metric_value': {'fontsize': 16, 'weight': 'bold'}
        }

    def black_scholes_greeks(self, S: float, K: float, T: float, r: float,
                           sigma: float, option_type: str = 'call') -> Dict[str, float]:
        """
        Calculate Black-Scholes Greeks including charm and vanna.

        Args:
            S: Current stock price
            K: Strike price
            T: Time to expiration (in years)
            r: Risk-free rate
            sigma: Volatility
            option_type: 'call' or 'put'

        Returns:
            Dictionary containing all Greeks
        """
        if T <= 0 or sigma <= 0:
            return {
                'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 'vega': 0.0,
                'rho': 0.0, 'charm': 0.0, 'vanna': 0.0
            }

        # Calculate d1 and d2
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)

        # Standard normal PDF and CDF
        N_d1 = norm.cdf(d1)
        N_d2 = norm.cdf(d2)
        n_d1 = norm.pdf(d1)

        # Basic Greeks
        if option_type.lower() == 'call':
            delta = N_d1
            rho = K * T * np.exp(-r * T) * N_d2
        else:
            delta = N_d1 - 1
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2)

        gamma = n_d1 / (S * sigma * np.sqrt(T))
        theta = (-S * n_d1 * sigma / (2 * np.sqrt(T)) -
                r * K * np.exp(-r * T) * (N_d2 if option_type.lower() == 'call' else norm.cdf(-d2)))
        vega = S * n_d1 * np.sqrt(T)

        # Higher-order Greeks
        charm = -n_d1 * (2 * r * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T))
        if option_type.lower() == 'put':
            charm += n_d1 / (sigma * np.sqrt(T))

        vanna = vega * (1 - d1) / (sigma * S)

        return {
            'delta': delta, 'gamma': gamma, 'theta': theta, 'vega': vega,
            'rho': rho, 'charm': charm, 'vanna': vanna
        }

    def calculate_portfolio_greeks(self, options_data: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """Calculate portfolio-level Greeks for options data."""
        results = []
        risk_free_rate = 0.05  # 5% risk-free rate

        for _, row in options_data.iterrows():
            # Extract required data
            strike = row['Strike']
            dte = row.get('DTE', 30)  # Default to 30 if not available
            option_type = 'call' if row.get('Call/Put', 'c').lower() == 'c' else 'put'

            # Convert DTE to years
            T = max(dte / 365.0, 1/365)  # Minimum 1 day

            # Estimate implied volatility (simplified)
            iv = 0.2 + 0.1 * abs(strike - current_price) / current_price

            # Calculate Greeks
            greeks = self.black_scholes_greeks(
                S=current_price,
                K=strike,
                T=T,
                r=risk_free_rate,
                sigma=iv,
                option_type=option_type
            )

            # Add to results - create a new dictionary to ensure all values are included
            result_row = {
                **row.to_dict(),  # Convert Series to dict
                **greeks,         # Add calculated Greeks
                'option_type': option_type,
                'time_to_expiry': T,
                'implied_vol': iv
            }
            results.append(result_row)

        return pd.DataFrame(results)

    def create_professional_charm_bomb_chart(self, charm_data: pd.DataFrame,
                                           current_price: float,
                                           save_path: Optional[str] = None,
                                           ticker: str = 'SPX') -> plt.Figure:
        """
        Create a professional "Charm Bomb" chart with dynamic price levels.
        """
        # Calculate dynamic price levels based on current price
        # Original SPX design: base=6181, flip=6185 (+4), bomb=6200 (+19), min=6150 (-31), max=6230 (+49)
        # Scale proportionally for any ticker
        spx_base = 6181
        scale_factor = current_price / spx_base

        price_base = current_price
        flip_point = current_price + (4 * scale_factor)  # Critical flip point
        bomb_point = current_price + (19 * scale_factor)  # Bomb point
        chart_min = current_price - (31 * scale_factor)   # Chart minimum
        chart_max = current_price + (49 * scale_factor)   # Chart maximum
        # Create figure with larger size for better readability
        fig = plt.figure(figsize=(14, 10), facecolor='white', dpi=200)
        fig.patch.set_facecolor('white')
        
        # Create sophisticated grid layout with larger main chart area
        gs = fig.add_gridspec(5, 3,
                             height_ratios=[0.6, 0.4, 0.8, 0.3, 3.0],
                             width_ratios=[1, 1, 1],
                             hspace=0.3, wspace=0.15,
                             left=0.08, right=0.95, top=0.92, bottom=0.08)
        
        # Professional title section
        title_ax = fig.add_subplot(gs[0, :])
        title_ax.set_xlim(0, 1)
        title_ax.set_ylim(0, 1)
        title_ax.axis('off')
        
        # Main title with professional styling
        if ticker == 'SPX':
            title_text = title_ax.text(0.5, 0.7, f'The Charm Bomb Above {flip_point:.0f}',
                                      ha='center', va='center', **self.fonts['title'])
            subtitle_text = title_ax.text(0.5, 0.2, 'Time Decay Becomes JPM\'s Enemy',
                                        ha='center', va='center', **self.fonts['subtitle'])
        else:
            title_text = title_ax.text(0.5, 0.7, f'{ticker} Charm Analysis',
                                      ha='center', va='center', **self.fonts['title'])
            subtitle_text = title_ax.text(0.5, 0.2, f'Professional Analysis for {ticker} at {current_price:.0f}',
                                        ha='center', va='center', **self.fonts['subtitle'])
        title_text.set_path_effects([path_effects.withStroke(linewidth=0, foreground='white')])
        
        # Professional critical alert box
        alert_ax = fig.add_subplot(gs[1, :])
        alert_ax.set_xlim(0, 1)
        alert_ax.set_ylim(0, 1)
        alert_ax.axis('off')
        
        # Create professional alert box with shadow
        alert_box = FancyBboxPatch((0.02, 0.1), 0.96, 0.8, 
                                  boxstyle="round,pad=0.03",
                                  facecolor=self.colors['bg_light_red'], 
                                  edgecolor=self.colors['error_red'],
                                  linewidth=1.5,
                                  alpha=0.95)
        alert_ax.add_patch(alert_box)
        
        # Alert text with professional formatting
        alert_text = (f'CRITICAL: Charm flips from ally to enemy at {flip_point:.0f} - '
                     'Creating explosive conditions')
        alert_ax.text(0.5, 0.5, alert_text, ha='center', va='center',
                     fontsize=16, color=self.colors['error_red'], weight='bold',
                     wrap=True)
        
        # Professional metric boxes
        metrics = [
            (f'Charm at {price_base-1:.0f}', '+186.4M', self.colors['success_green']),
            (f'Charm at {flip_point:.0f} (Flip Point)', '-31.7M', self.colors['error_red']),
            (f'Charm at {bomb_point:.0f} (BOMB)', '-3.5B', self.colors['error_red'])
        ]
        
        for i, (title, value, color) in enumerate(metrics):
            metric_ax = fig.add_subplot(gs[2, i])
            metric_ax.set_xlim(0, 1)
            metric_ax.set_ylim(0, 1)
            metric_ax.axis('off')
            
            # Professional metric box with subtle shadow
            box = FancyBboxPatch((0.05, 0.1), 0.9, 0.8, 
                               boxstyle="round,pad=0.04",
                               facecolor=self.colors['white'], 
                               edgecolor=self.colors['border_medium'],
                               linewidth=1,
                               alpha=0.98)
            metric_ax.add_patch(box)
            
            # Metric title
            metric_ax.text(0.5, 0.75, title, ha='center', va='center', 
                          **self.fonts['metric_title'])
            
            # Metric value with color
            metric_ax.text(0.5, 0.35, value, ha='center', va='center', 
                          fontsize=self.fonts['metric_value']['fontsize'],
                          weight=self.fonts['metric_value']['weight'],
                          color=color)
        
        # Professional main chart (larger area for better readability)
        main_ax = fig.add_subplot(gs[4, :])
        
        # Create smooth, professional data with dynamic ranges
        strikes = np.linspace(chart_min, chart_max, 200)  # More points for smoother curves

        # Professional teal line (smooth exponential decay) - scaled dynamically
        teal_charm = np.where(strikes <= price_base,
                             3200 * np.exp(-((strikes - chart_min) / (12 * scale_factor))**1.5) + 600,
                             600 * np.exp(-((strikes - price_base) / (8 * scale_factor))**2))
        teal_charm = np.where(strikes > flip_point, 0, teal_charm)

        # Professional orange line (smooth transition to negative) - scaled dynamically
        orange_charm = np.where(strikes <= price_base,
                               200 + 300 * (strikes - chart_min) / (31 * scale_factor),
                               -200 - 1800 * ((strikes - price_base) / (25 * scale_factor))**1.2)

        # Add smooth oscillation for the negative region - scaled dynamically
        orange_charm = np.where(strikes > bomb_point,
                               -3200 + 400 * np.sin((strikes - bomb_point) / (8 * scale_factor)) * np.exp(-(strikes - bomb_point) / (30 * scale_factor)),
                               orange_charm)
        
        # Professional background regions with gradients - dynamic ranges
        main_ax.axvspan(chart_min, price_base, color=self.colors['bg_light_green'], alpha=0.4, zorder=0)
        main_ax.axvspan(price_base, chart_max, color=self.colors['bg_light_red'], alpha=0.4, zorder=0)
        
        # Plot professional lines with anti-aliasing
        teal_line = main_ax.plot(strikes, teal_charm, color=self.colors['primary_teal'], 
                                linewidth=3.5, zorder=3, alpha=0.9, 
                                solid_capstyle='round', solid_joinstyle='round')
        
        orange_line = main_ax.plot(strikes, orange_charm, color=self.colors['primary_orange'], 
                                  linewidth=3.5, zorder=3, alpha=0.9,
                                  solid_capstyle='round', solid_joinstyle='round')
        
        # Professional critical line - dynamic position
        main_ax.axvline(x=current_price, color=self.colors['warning_orange'],
                       linestyle='--', linewidth=2.5, zorder=4, alpha=0.8)
        
        # Professional zero line
        main_ax.axhline(y=0, color=self.colors['text_muted'], linewidth=1, alpha=0.6, zorder=1)
        
        # Professional annotations with callout boxes
        # Positive charm annotation
        pos_box = dict(boxstyle="round,pad=0.5", facecolor=self.colors['white'],
                      edgecolor=self.colors['success_green'], linewidth=1.5, alpha=0.95)
        main_ax.text(current_price - 16*scale_factor, 2200, 'POSITIVE CHARM\nTime helps positions',
                    fontsize=14, color=self.colors['success_green'], weight='bold',
                    ha='center', va='center', bbox=pos_box)
        
        # Negative charm annotation with professional styling
        neg_box = dict(boxstyle="round,pad=0.5", facecolor=self.colors['white'],
                      edgecolor=self.colors['error_red'], linewidth=1.5, alpha=0.95)
        main_ax.text(bomb_point + 5*scale_factor, -2800, '-3.5 BILLION\nCHARM BOMB\nNEGATIVE CHARM\nTime destroys positions',
                    fontsize=14, color=self.colors['error_red'], weight='bold',
                    ha='center', va='center', bbox=neg_box)
        
        # Professional arrows
        arrow_props = dict(arrowstyle='->', color=self.colors['error_red'],
                          lw=2, alpha=0.8)
        main_ax.annotate('', xy=(bomb_point, -1200), xytext=(bomb_point - 5*scale_factor, -800),
                        arrowprops=arrow_props)
        main_ax.annotate('', xy=(bomb_point + 10*scale_factor, -800), xytext=(bomb_point + 15*scale_factor, -400),
                        arrowprops=arrow_props)
        
        # Current price line label
        main_ax.text(current_price, 3500, f'{ticker} {current_price:.0f}', ha='center', va='bottom',
                    fontsize=10, color=self.colors['warning_orange'], weight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['white'],
                             edgecolor=self.colors['warning_orange'], alpha=0.9))
        
        # Professional styling
        main_ax.set_xlabel('Strike Price', **self.fonts['label'])
        main_ax.set_ylabel('Charm (x 0) - Millions', **self.fonts['label'])
        main_ax.set_title('Charm Profile: The Time Decay Battlefield', 
                         **self.fonts['section_title'], pad=25)
        
        # Professional grid
        main_ax.grid(True, alpha=0.3, linewidth=0.5, color=self.colors['border_light'])
        main_ax.set_axisbelow(True)
        
        # Professional axis styling
        main_ax.spines['top'].set_visible(False)
        main_ax.spines['right'].set_visible(False)
        main_ax.spines['left'].set_color(self.colors['border_medium'])
        main_ax.spines['bottom'].set_color(self.colors['border_medium'])
        
        # Set professional limits and ticks - dynamic ranges
        main_ax.set_xlim(chart_min, chart_max)
        main_ax.set_ylim(-4000, 4000)
        main_ax.tick_params(colors=self.colors['text_secondary'], labelsize=10)
        
        # Professional layout
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=200, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"Professional Charm Bomb chart saved to {save_path}")
        
        return fig
    
    def create_professional_gamma_charm_interaction_chart(self, interaction_data: pd.DataFrame,
                                                        current_price: float,
                                                        save_path: Optional[str] = None,
                                                        ticker: str = 'SPX') -> plt.Figure:
        """
        Create a professional "Gamma-Charm Interaction" chart.
        """
        fig, ax = plt.subplots(figsize=(12, 8), facecolor='white', dpi=200)
        fig.patch.set_facecolor('white')
        
        # Calculate dynamic price levels
        spx_base = 6181
        scale_factor = current_price / spx_base

        # Professional data setup with dynamic strikes
        np.random.seed(42)
        # Create dynamic strike array based on current price
        strike_range = np.arange(current_price - 31*scale_factor, current_price + 39*scale_factor, 5*scale_factor)
        strikes = strike_range[:15]  # Take first 15 strikes to match original
        
        # Professional blue bars with gradient effect
        bar_heights = [100, 0, 0, 0, 75, 50, 20, 40, 35, 300, 180, 130, 90, 0, 20]
        
        # Create professional bars with dynamic width
        bar_width = 4 * scale_factor  # Scale bar width proportionally
        bars = ax.bar(strikes, bar_heights, width=bar_width,
                     color=self.colors['primary_blue'], alpha=0.3,
                     edgecolor=self.colors['primary_blue'], linewidth=0.5,
                     zorder=1)
        
        # Professional scatter points with precise sizing and colors - dynamic positions
        scatter_data = [
            (current_price - 31*scale_factor, 80, 800, self.colors['success_green']),
            (current_price - 21*scale_factor, 25, 300, self.colors['success_green']),
            (current_price - 6*scale_factor, 45, 500, self.colors['success_green']),
            (current_price - 1*scale_factor, 35, 400, self.colors['warning_orange']),
            (current_price + 4*scale_factor, 20, 250, self.colors['error_red']),
            (current_price + 9*scale_factor, 30, 350, self.colors['error_red']),
            (current_price + 14*scale_factor, 180, 2000, self.colors['error_red']),  # Large explosive point
            (current_price + 19*scale_factor, 90, 1200, self.colors['error_red']),
            (current_price + 24*scale_factor, 70, 900, self.colors['error_red']),
            (current_price + 39*scale_factor, 15, 200, self.colors['error_red'])
        ]
        
        # Plot professional scatter points
        for strike, gamma, size, color in scatter_data:
            # All points get clean, professional circles
            ax.scatter(strike, gamma, s=size, c=color, alpha=0.8, 
                      edgecolors='white', linewidth=1.5, zorder=4)
        
        # Professional critical line - dynamic position
        ax.axvline(x=current_price, color=self.colors['warning_orange'],
                  linestyle='--', linewidth=3, zorder=5, alpha=0.9)
        
        # Professional annotations - dynamic position and larger font
        ax.text(current_price, 290, 'CHARM FLIPS', ha='center', va='bottom',
               fontsize=16, color=self.colors['warning_orange'], weight='bold',
               bbox=dict(boxstyle="round,pad=0.4", facecolor=self.colors['white'],
                        edgecolor=self.colors['warning_orange'], alpha=0.95))
        
        # Professional explosive mix annotation - dynamic position and larger font
        explosive_box = dict(boxstyle="round,pad=0.5", facecolor=self.colors['bg_light_red'],
                           edgecolor=self.colors['error_red'], linewidth=1.5, alpha=0.95)
        ax.text(current_price + 16*scale_factor, 80, 'EXPLOSIVE MIX\nGAMMA + CHARM', ha='center', va='center',
               fontsize=16, color=self.colors['error_red'], weight='bold',
               bbox=explosive_box)
        
        # Professional stability annotation - dynamic position and larger font
        stability_box = dict(boxstyle="round,pad=0.4", facecolor=self.colors['bg_light_green'],
                           edgecolor=self.colors['success_green'], linewidth=1.5, alpha=0.95)
        ax.text(current_price - 21*scale_factor, 20, 'Gamma + Positive Charm = Stability',
               fontsize=14, color=self.colors['success_green'], weight='bold',
               bbox=stability_box)
        
        # Professional explosion annotation - dynamic position and larger font
        explosion_box = dict(boxstyle="round,pad=0.4", facecolor=self.colors['bg_light_red'],
                           edgecolor=self.colors['error_red'], linewidth=1.5, alpha=0.95)
        ax.text(current_price + 24*scale_factor, 20, 'Gamma + Negative Charm = EXPLOSION',
               fontsize=14, color=self.colors['error_red'], weight='bold',
               bbox=explosion_box)
        
        # Professional legend - dynamic position and larger fonts
        legend_x = current_price + 31*scale_factor
        legend_y = 260
        sizes = [500, 1000, 3000]
        labels = ['500M', '1000M', '3000M']

        # Legend title
        ax.text(legend_x, legend_y + 25, 'Charm Size & Color:',
               fontsize=14, weight='bold', color=self.colors['text_primary'])
        
        # Legend items - larger fonts
        for i, (size, label) in enumerate(zip(sizes, labels)):
            y_pos = legend_y - i * 20
            ax.scatter(legend_x, y_pos, s=size/12, c=self.colors['text_muted'], alpha=0.7,
                      edgecolors='white', linewidth=1)
            ax.text(legend_x + 6*scale_factor, y_pos, label, va='center', fontsize=12,
                   color=self.colors['text_secondary'])
        
        # Professional styling
        ax.set_xlabel('Strike Price', **self.fonts['label'])
        ax.set_ylabel('Gamma (Blue Bars)', **self.fonts['label'])
        ax.set_title('The Gamma-Charm Interaction', **self.fonts['title'], pad=25)
        
        # Professional grid and spines
        ax.grid(True, alpha=0.3, linewidth=0.5, color=self.colors['border_light'])
        ax.set_axisbelow(True)
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_color(self.colors['border_medium'])
        ax.spines['bottom'].set_color(self.colors['border_medium'])
        
        # Professional limits and ticks - dynamic ranges
        chart_min = current_price - 35*scale_factor
        chart_max = current_price + 45*scale_factor
        ax.set_xlim(chart_min, chart_max)
        ax.set_ylim(0, 320)
        ax.tick_params(colors=self.colors['text_secondary'], labelsize=12)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=200, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"Professional Gamma-Charm Interaction chart saved to {save_path}")
        
        return fig
    
    def create_professional_vanna_charm_double_trap_chart(self, vanna_charm_data: pd.DataFrame,
                                                        current_price: float,
                                                        save_path: Optional[str] = None,
                                                        ticker: str = 'SPX') -> plt.Figure:
        """
        Create a professional "Vanna-Charm Double Trap" chart with dynamic price levels.
        """
        # Calculate dynamic price levels first
        spx_base = 6181
        scale_factor = current_price / spx_base
        flip_point = current_price + (4 * scale_factor)
        bomb_point = current_price + (19 * scale_factor)  # Add bomb point for annotations
        chart_min = current_price - (31 * scale_factor)
        chart_max = current_price + (39 * scale_factor)

        fig = plt.figure(figsize=(16, 12), facecolor='white', dpi=200)
        fig.patch.set_facecolor('white')
        
        # Professional layout
        gs = fig.add_gridspec(5, 2, height_ratios=[0.6, 0.8, 1.2, 0.3, 3.5], 
                             width_ratios=[1, 1], hspace=0.4, wspace=0.2,
                             left=0.08, right=0.95, top=0.92, bottom=0.08)
        
        # Professional title section
        title_ax = fig.add_subplot(gs[0, :])
        title_ax.set_xlim(0, 1)
        title_ax.set_ylim(0, 1)
        title_ax.axis('off')
        
        # Main title - dynamic ticker and price
        title_text = title_ax.text(0.5, 0.7, f'The Vanna-Charm Double Trap at {flip_point:.0f}',
                                  ha='center', va='center', **self.fonts['title'])
        title_text.set_path_effects([path_effects.withStroke(linewidth=0, foreground='white')])
        
        # Subtitle
        title_ax.text(0.5, 0.2, 'How Two Greeks Create the Perfect Trap', 
                     ha='center', va='center', **self.fonts['subtitle'])
        
        # Professional critical discovery box
        alert_ax = fig.add_subplot(gs[1, :])
        alert_ax.set_xlim(0, 1)
        alert_ax.set_ylim(0, 1)
        alert_ax.axis('off')
        
        alert_box = FancyBboxPatch((0.02, 0.1), 0.96, 0.8, 
                                  boxstyle="round,pad=0.03",
                                  facecolor=self.colors['bg_light_red'], 
                                  edgecolor=self.colors['error_red'],
                                  linewidth=1.5, alpha=0.95)
        alert_ax.add_patch(alert_box)
        
        alert_text = (f"CRITICAL DISCOVERY: Both Vanna and Charm flip at {flip_point:.0f}\n"
                     f"They desperately need {ticker} to stay under {flip_point:.0f} to avoid the doom loop!")
        alert_ax.text(0.5, 0.5, alert_text, ha='center', va='center',
                     fontsize=16, color=self.colors['error_red'], weight='bold')
        
        # Professional regime comparison boxes
        regime_ax = fig.add_subplot(gs[2, :])
        regime_ax.set_xlim(0, 1)
        regime_ax.set_ylim(0, 1)
        regime_ax.axis('off')
        
        # Left box - Stable Regime
        left_box = FancyBboxPatch((0.02, 0.1), 0.46, 0.8, 
                                 boxstyle="round,pad=0.03",
                                 facecolor=self.colors['bg_light_green'], 
                                 edgecolor=self.colors['success_green'],
                                 linewidth=2, alpha=0.95)
        regime_ax.add_patch(left_box)
        
        stable_text = (f"Below {flip_point:.0f}: Stable Regime\n"
                      "Vanna SHORT + Charm POSITIVE = Self-stabilizing")
        regime_ax.text(0.25, 0.5, stable_text, ha='center', va='center', 
                      fontsize=10, color=self.colors['success_green'], weight='normal')
        
        # Right box - Explosive Regime
        right_box = FancyBboxPatch((0.52, 0.1), 0.46, 0.8, 
                                  boxstyle="round,pad=0.03",
                                  facecolor=self.colors['bg_light_red'], 
                                  edgecolor=self.colors['error_red'],
                                  linewidth=2, alpha=0.95)
        regime_ax.add_patch(right_box)
        
        explosive_text = (f"Above {flip_point:.0f}: Explosive Regime\n"
                         "Vanna LONG + Charm NEGATIVE = Self-destructing")
        regime_ax.text(0.75, 0.5, explosive_text, ha='center', va='center', 
                      fontsize=10, color=self.colors['error_red'], weight='normal')
        
        # Professional main chart
        main_ax = fig.add_subplot(gs[4, :])
        
        # Create smooth professional data with dynamic ranges
        strikes = np.linspace(chart_min, chart_max, 200)

        # Professional Vanna line (smooth S-curve) - scaled dynamically
        vanna = np.where(strikes < flip_point,
                        -8000 + 6000 / (1 + np.exp(-0.3 * (strikes - (current_price - 11*scale_factor)) / scale_factor)),
                        2000 + 8000 / (1 + np.exp(-0.2 * (strikes - (current_price + 14*scale_factor)) / scale_factor)))
        vanna = np.where(strikes > (current_price + 29*scale_factor),
                        12000 - 4000 * (strikes - (current_price + 29*scale_factor)) / (10*scale_factor), vanna)

        # Professional Charm line (smooth exponential transition) - scaled dynamically
        charm = np.where(strikes < flip_point,
                        1800 * np.exp(-((strikes - chart_min) / (20*scale_factor))**1.2),
                        -300 - 3000 * ((strikes - flip_point) / (25*scale_factor))**1.5)
        
        # Professional background regions - dynamic ranges
        main_ax.axvspan(chart_min, flip_point, color=self.colors['bg_light_green'], alpha=0.3, zorder=0)
        main_ax.axvspan(flip_point, chart_max, color=self.colors['bg_light_red'], alpha=0.3, zorder=0)
        
        # Create twin axis for professional dual-axis display
        charm_ax = main_ax.twinx()
        
        # Plot professional lines
        vanna_line = main_ax.plot(strikes, vanna, color=self.colors['primary_blue'], 
                                 linewidth=4, label='Vanna', zorder=3, alpha=0.9,
                                 solid_capstyle='round', solid_joinstyle='round')
        charm_line = charm_ax.plot(strikes, charm, color=self.colors['primary_red'], 
                                  linewidth=4, label='Charm', zorder=3, alpha=0.9,
                                  solid_capstyle='round', solid_joinstyle='round')
        
        # Professional critical line - dynamic position
        main_ax.axvline(x=flip_point, color=self.colors['warning_orange'],
                       linestyle='--', linewidth=3, zorder=4, alpha=0.9)
        
        # Professional regime labels
        stable_box = dict(boxstyle="round,pad=0.5", facecolor=self.colors['bg_light_green'],
                         edgecolor=self.colors['success_green'], linewidth=1.5, alpha=0.95)
        main_ax.text(flip_point - 18*scale_factor, 8000, 'STABLE REGIME\nVanna short + Charm positive',
                    ha='center', va='center', fontsize=14,
                    color=self.colors['success_green'], weight='bold', bbox=stable_box)
        
        explosive_box = dict(boxstyle="round,pad=0.5", facecolor=self.colors['bg_light_red'],
                           edgecolor=self.colors['error_red'], linewidth=1.5, alpha=0.95)
        main_ax.text(bomb_point + 2*scale_factor, 8000, 'EXPLOSIVE REGIME\nVanna long + Charm negative',
                    ha='center', va='center', fontsize=14,
                    color=self.colors['error_red'], weight='bold', bbox=explosive_box)
        
        # Professional annotations with callouts - dynamic ticker and price
        main_ax.text(flip_point, 12000, f'{ticker} {current_price:.0f}', ha='center', va='bottom',
                    fontsize=14, color=self.colors['warning_orange'], weight='bold',
                    bbox=dict(boxstyle="round,pad=0.3", facecolor=self.colors['white'],
                             edgecolor=self.colors['warning_orange'], alpha=0.95))
        
        # Regime change marker
        regime_box = dict(boxstyle="round,pad=0.4", facecolor='yellow',
                         edgecolor=self.colors['warning_orange'], linewidth=2, alpha=0.9)
        main_ax.text(flip_point, 2000, f'REGIME\nCHANGE\n{flip_point:.0f}', ha='center', va='center',
                    fontsize=14, color=self.colors['warning_orange'], weight='bold',
                    bbox=regime_box)
        
        # Professional value callouts
        vanna_callout = dict(boxstyle="round,pad=0.4", facecolor=self.colors['white'],
                           edgecolor=self.colors['primary_blue'], linewidth=1.5, alpha=0.95)
        main_ax.annotate('Vanna: +23K\nEXPLOSIVE REGIME',
                        xy=(bomb_point, 10000), xytext=(bomb_point + 10*scale_factor, 6000),
                        fontsize=14, color=self.colors['primary_blue'], weight='bold',
                        arrowprops=dict(arrowstyle='->', color=self.colors['primary_blue'], lw=2),
                        bbox=vanna_callout)
        
        charm_callout = dict(boxstyle="round,pad=0.4", facecolor=self.colors['white'],
                           edgecolor=self.colors['primary_red'], linewidth=1.5, alpha=0.95)
        charm_ax.annotate('Charm: -3.5B',
                         xy=(bomb_point + 5*scale_factor, -3000), xytext=(bomb_point + 10*scale_factor, -1500),
                         fontsize=14, color=self.colors['primary_red'], weight='bold',
                         arrowprops=dict(arrowstyle='->', color=self.colors['primary_red'], lw=2),
                         bbox=charm_callout)
        
        # Professional styling
        main_ax.set_xlabel('Strike Price', **self.fonts['label'])
        main_ax.set_ylabel('Vanna (Thousands)', fontsize=self.fonts['label']['fontsize'], 
                          weight=self.fonts['label']['weight'], color=self.colors['primary_blue'])
        charm_ax.set_ylabel('Charm (Millions)', fontsize=self.fonts['label']['fontsize'], 
                           weight=self.fonts['label']['weight'], color=self.colors['primary_red'])
        main_ax.set_title('Vanna & Charm Profile: The Double Trap', 
                         **self.fonts['section_title'], pad=25)
        
        # Professional axis styling
        main_ax.tick_params(axis='y', labelcolor=self.colors['primary_blue'], labelsize=10)
        charm_ax.tick_params(axis='y', labelcolor=self.colors['primary_red'], labelsize=10)
        main_ax.tick_params(axis='x', labelcolor=self.colors['text_secondary'], labelsize=10)
        
        # Professional legend
        lines1, labels1 = main_ax.get_legend_handles_labels()
        lines2, labels2 = charm_ax.get_legend_handles_labels()
        main_ax.legend(lines1 + lines2, labels1 + labels2, loc='upper left',
                      frameon=True, fancybox=True, shadow=True)
        
        # Professional grid and spines
        main_ax.grid(True, alpha=0.3, linewidth=0.5, color=self.colors['border_light'])
        main_ax.set_axisbelow(True)
        main_ax.spines['top'].set_visible(False)
        main_ax.spines['left'].set_color(self.colors['primary_blue'])
        main_ax.spines['bottom'].set_color(self.colors['border_medium'])
        charm_ax.spines['top'].set_visible(False)
        charm_ax.spines['right'].set_color(self.colors['primary_red'])
        
        # Professional limits - dynamic ranges
        main_ax.set_xlim(chart_min, chart_max)
        main_ax.set_ylim(-15000, 15000)
        charm_ax.set_ylim(-4000, 2500)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=200, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"Professional Vanna-Charm Double Trap chart saved to {save_path}")
        
        return fig

    # Wrapper methods to match our system's expected interface
    def create_charm_profile_chart(self, options_data: pd.DataFrame, current_price: float,
                                 output_path: str = None, ticker: str = 'SPX') -> str:
        """Create charm profile chart and return the file path."""
        # Calculate Greeks if not already present
        if 'charm' not in options_data.columns:
            options_data = self.calculate_portfolio_greeks(options_data, current_price)

        # Use provided output_path or default
        if output_path is None:
            output_path = os.path.join(f"output/{CHARTS_DIR}", CHARM_PROFILE_CHART_NAME)

        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Remove existing file if it exists
        if os.path.exists(output_path):
            os.remove(output_path)

        fig = self.create_professional_charm_bomb_chart(
            options_data, current_price, output_path, ticker
        )
        plt.close(fig)
        return output_path

    def create_gamma_charm_interaction_chart(self, options_data: pd.DataFrame, current_price: float,
                                           output_path: str = None, ticker: str = 'SPX') -> str:
        """Create gamma-charm interaction chart and return the file path."""
        # Calculate Greeks if not already present
        if 'charm' not in options_data.columns:
            options_data = self.calculate_portfolio_greeks(options_data, current_price)

        # Use provided output_path or default
        if output_path is None:
            output_path = os.path.join(f"output/{CHARTS_DIR}", GAMMA_CHARM_INTERACTION_CHART_NAME)

        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Remove existing file if it exists
        if os.path.exists(output_path):
            os.remove(output_path)

        fig = self.create_professional_gamma_charm_interaction_chart(
            options_data, current_price, output_path, ticker
        )
        plt.close(fig)
        return output_path

    def create_vanna_charm_double_trap_chart(self, options_data: pd.DataFrame, current_price: float,
                                           output_path: str = None, ticker: str = 'SPX') -> str:
        """Create vanna-charm double trap chart and return the file path."""
        # Calculate Greeks if not already present
        if 'charm' not in options_data.columns:
            options_data = self.calculate_portfolio_greeks(options_data, current_price)

        # Use provided output_path or default
        if output_path is None:
            output_path = os.path.join(f"output/{CHARTS_DIR}", VANNA_CHARM_DOUBLE_TRAP_CHART_NAME)

        # Ensure directory exists
        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        # Remove existing file if it exists
        if os.path.exists(output_path):
            os.remove(output_path)

        fig = self.create_professional_vanna_charm_double_trap_chart(
            options_data, current_price, output_path, ticker
        )
        plt.close(fig)
        return output_path


def main():
    """
    Test the professional chart generator.
    """
    from data_loader import OptionsDataLoader
    from analytics_engine import OptionsAnalyticsEngine
    
    # Initialize components
    loader = OptionsDataLoader('/home/<USER>/upload/spx_complete_2025_q2.csv')
    analytics = OptionsAnalyticsEngine()
    charts = AdvancedChartGenerator()
    
    # Load sample data
    print("Loading data for professional chart generation...")
    data = loader.load_data()
    date_data = loader.filter_by_date('2025-04-01')
    
    current_price = 6181
    filtered_data = loader.filter_by_strike_range(date_data, 
                                                 current_price - 50, 
                                                 current_price + 50)
    
    # Calculate analytics
    charm_profile = analytics.calculate_charm_profile(
        filtered_data, (current_price - 35, current_price + 35))
    gamma_charm = analytics.calculate_gamma_charm_interaction(filtered_data)
    vanna_charm = analytics.calculate_vanna_charm_double_trap(
        filtered_data, (current_price - 35, current_price + 35))
    
    print("Generating professional charts...")
    
    # Generate professional charts
    os.makedirs('professional_output', exist_ok=True)
    
    # Chart 1: Professional Charm Bomb
    fig1 = charts.create_professional_charm_bomb_chart(
        charm_profile, current_price, 
        'professional_output/charm_bomb_professional.png')
    
    # Chart 2: Professional Gamma-Charm Interaction
    fig2 = charts.create_professional_gamma_charm_interaction_chart(
        gamma_charm, current_price, 
        'professional_output/gamma_charm_interaction_professional.png')
    
    # Chart 3: Professional Vanna-Charm Double Trap
    fig3 = charts.create_professional_vanna_charm_double_trap_chart(
        vanna_charm, current_price, 
        'professional_output/vanna_charm_double_trap_professional.png')
    
    print("Professional charts generated successfully!")
    print("Charts saved in 'professional_output' directory")


if __name__ == "__main__":
    import os
    main()


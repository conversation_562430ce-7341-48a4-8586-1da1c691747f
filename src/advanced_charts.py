"""
Advanced Chart Generator Module

This module provides functionality to generate three advanced options analytics charts:
1. Charm Profile: The Time Decay Battlefield
2. Gamma-Charm Interaction scatter plot
3. Vanna-Charm Double Trap chart

Integrated into the Hidden Outliers Analysis system.
"""

import matplotlib.pyplot as plt
import matplotlib.patches as patches
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import seaborn as sns
from matplotlib.patches import Rectangle
import warnings
import os
from scipy.stats import norm

warnings.filterwarnings('ignore')

# Set style
plt.style.use('default')
sns.set_palette("husl")


class AdvancedChartGenerator:
    """
    A class to generate advanced options analytics charts.
    
    This class provides methods to create professional-looking charts
    that match institutional analytics standards.
    """
    
    def __init__(self, figsize: Tuple[int, int] = (14, 10)):
        """
        Initialize the advanced chart generator.
        
        Args:
            figsize (Tuple[int, int]): Default figure size
        """
        self.figsize = figsize
        self.colors = {
            'positive_charm': '#2E8B57',  # Sea green
            'negative_charm': '#DC143C',  # Crimson
            'neutral': '#4682B4',         # Steel blue
            'background_positive': '#E6F3E6',  # Light green
            'background_negative': '#FFE6E6',  # Light red
            'critical_line': '#FF8C00',   # Dark orange
            'text_critical': '#B22222'    # Fire brick
        }
    
    def black_scholes_greeks(self, S: float, K: float, T: float, r: float, 
                           sigma: float, option_type: str = 'call') -> Dict[str, float]:
        """
        Calculate Black-Scholes Greeks for a single option.
        
        Args:
            S (float): Current stock price
            K (float): Strike price
            T (float): Time to expiration (in years)
            r (float): Risk-free rate
            sigma (float): Implied volatility
            option_type (str): 'call' or 'put'
            
        Returns:
            Dict[str, float]: Dictionary containing all Greeks
        """
        if T <= 0 or sigma <= 0:
            return {
                'delta': 0.0, 'gamma': 0.0, 'theta': 0.0, 
                'vega': 0.0, 'rho': 0.0, 'charm': 0.0, 'vanna': 0.0
            }
        
        # Calculate d1 and d2
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        # Standard normal PDF and CDF
        N_d1 = norm.cdf(d1)
        N_d2 = norm.cdf(d2)
        n_d1 = norm.pdf(d1)
        
        # Calculate Greeks
        if option_type.lower() == 'call':
            delta = N_d1
            theta = (-S * n_d1 * sigma / (2 * np.sqrt(T)) - 
                    r * K * np.exp(-r * T) * N_d2) / 365
            rho = K * T * np.exp(-r * T) * N_d2 / 100
        else:  # put
            delta = N_d1 - 1
            theta = (-S * n_d1 * sigma / (2 * np.sqrt(T)) + 
                    r * K * np.exp(-r * T) * norm.cdf(-d2)) / 365
            rho = -K * T * np.exp(-r * T) * norm.cdf(-d2) / 100
        
        gamma = n_d1 / (S * sigma * np.sqrt(T))
        vega = S * n_d1 * np.sqrt(T) / 100
        
        # Second-order Greeks
        charm = -n_d1 * (2 * r * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T))
        if option_type.lower() == 'put':
            charm = charm - n_d1 / (sigma * np.sqrt(T))
            
        vanna = vega * d2 / sigma
        
        return {
            'delta': delta,
            'gamma': gamma,
            'theta': theta,
            'vega': vega,
            'rho': rho,
            'charm': charm,
            'vanna': vanna
        }
    
    def calculate_portfolio_greeks(self, options_data: pd.DataFrame, current_price: float) -> pd.DataFrame:
        """
        Calculate portfolio-level Greeks for options data.

        Args:
            options_data (pd.DataFrame): Options data with required columns
            current_price (float): Current underlying price

        Returns:
            pd.DataFrame: Data with calculated Greeks
        """
        results = []
        risk_free_rate = 0.05  # 5% risk-free rate

        for _, row in options_data.iterrows():
            # Extract required data
            strike = row['Strike']
            dte = row.get('DTE', 30)  # Default to 30 if not available
            option_type = 'call' if row.get('Call/Put', 'c').lower() == 'c' else 'put'
            
            # Use implied volatility if available, otherwise estimate
            if 'Implied Volatility' in row and pd.notna(row['Implied Volatility']):
                iv = row['Implied Volatility']
            elif 'Bid Implied Volatility' in row and pd.notna(row['Bid Implied Volatility']):
                iv = row['Bid Implied Volatility']
            elif 'Ask Implied Volatility' in row and pd.notna(row['Ask Implied Volatility']):
                iv = row['Ask Implied Volatility']
            else:
                # Estimate IV based on moneyness
                moneyness = strike / current_price
                if 0.95 <= moneyness <= 1.05:
                    iv = 0.20  # ATM
                elif moneyness < 0.95:
                    iv = 0.25  # ITM
                else:
                    iv = 0.18  # OTM
            
            # Calculate time to expiry in years
            T = max(dte / 365.0, 1/365)  # Minimum 1 day
            
            # Calculate Greeks
            greeks = self.black_scholes_greeks(
                S=current_price,
                K=strike,
                T=T,
                r=risk_free_rate,
                sigma=iv,
                option_type=option_type
            )

            # Add to results - create a new dictionary to ensure all values are included
            result_row = {
                **row.to_dict(),  # Convert Series to dict
                **greeks,         # Add calculated Greeks
                'option_type': option_type,
                'time_to_expiry': T,
                'implied_vol': iv
            }
            results.append(result_row)

        return pd.DataFrame(results)
    
    def prepare_charm_profile_data(self, options_data: pd.DataFrame,
                                 current_price: float,
                                 strike_range: Tuple[float, float]) -> pd.DataFrame:
        """
        Prepare data for Charm Profile chart.

        Args:
            options_data (pd.DataFrame): Options data (will calculate Greeks if needed)
            current_price (float): Current underlying price
            strike_range (Tuple[float, float]): (min_strike, max_strike)

        Returns:
            pd.DataFrame: Prepared charm profile data
        """
        # First calculate Greeks if not already present
        options_with_greeks = self.calculate_portfolio_greeks(options_data, current_price)

        # Check if charm column exists
        if 'charm' not in options_with_greeks.columns:
            return pd.DataFrame()  # Return empty DataFrame

        # Filter by strike range
        min_strike, max_strike = strike_range
        filtered_data = options_with_greeks[
            (options_with_greeks['Strike'] >= min_strike) &
            (options_with_greeks['Strike'] <= max_strike)
        ].copy()

        if len(filtered_data) == 0:
            return pd.DataFrame()

        # Group by strike and option type, aggregate charm
        charm_profile = filtered_data.groupby(['Strike', 'option_type']).agg({
            'charm': 'sum',
            'Open Interest': 'sum'
        }).reset_index()

        # Rename columns for consistency
        charm_profile.columns = ['strike', 'option_type', 'charm', 'open_interest']

        return charm_profile
    
    def create_charm_profile_chart(self, options_data: pd.DataFrame, 
                                 current_price: float,
                                 output_path: str,
                                 strike_range_pct: float = 0.05) -> str:
        """
        Create the Charm Profile: Time Decay Battlefield chart.
        
        Args:
            options_data (pd.DataFrame): Options data
            current_price (float): Current underlying price
            output_path (str): Path to save the chart
            strike_range_pct (float): Strike range as percentage of current price
            
        Returns:
            str: Path to the generated chart
        """
        # Calculate Greeks for all options
        options_with_greeks = self.calculate_portfolio_greeks(options_data, current_price)
        
        # Define strike range
        strike_range = current_price * strike_range_pct
        min_strike = current_price - strike_range
        max_strike = current_price + strike_range
        
        # Prepare charm profile data
        charm_data = self.prepare_charm_profile_data(
            options_with_greeks, current_price, (min_strike, max_strike))
        
        if len(charm_data) == 0:
            print("Warning: No data available for Charm Profile chart")
            return output_path
        
        # Create the chart
        fig, ax = plt.subplots(figsize=self.figsize)
        
        # Filter for calls and puts
        calls_data = charm_data[charm_data['option_type'] == 'call'].copy()
        puts_data = charm_data[charm_data['option_type'] == 'put'].copy()
        
        if len(calls_data) == 0 and len(puts_data) == 0:
            print("Warning: No calls or puts data for Charm Profile chart")
            return output_path
        
        # Convert Charm to thousands for display
        if len(calls_data) > 0:
            calls_data['charm_display'] = calls_data['charm'] * 1000
        if len(puts_data) > 0:
            puts_data['charm_display'] = puts_data['charm'] * 1000
        
        # Create background regions
        strike_min = charm_data['strike'].min()
        strike_max = charm_data['strike'].max()
        
        # Calculate charm range for background
        all_charm = []
        if len(calls_data) > 0:
            all_charm.extend(calls_data['charm_display'].tolist())
        if len(puts_data) > 0:
            all_charm.extend(puts_data['charm_display'].tolist())
        
        if all_charm:
            charm_min = min(all_charm)
            charm_max = max(all_charm)
            
            # Positive charm region (left side of current price)
            if charm_max > 0:
                positive_region = Rectangle((strike_min, 0), current_price - strike_min, charm_max,
                                          facecolor=self.colors['background_positive'], alpha=0.3, zorder=0)
                ax.add_patch(positive_region)
            
            # Negative charm region (right side of current price)
            if charm_min < 0:
                negative_region = Rectangle((current_price, charm_min), strike_max - current_price, -charm_min,
                                          facecolor=self.colors['background_negative'], alpha=0.3, zorder=0)
                ax.add_patch(negative_region)
        
        # Plot Charm profiles
        if len(calls_data) > 0:
            ax.plot(calls_data['strike'], calls_data['charm_display'], 
                   color=self.colors['positive_charm'], linewidth=3, label='Calls Charm', zorder=2)
        
        if len(puts_data) > 0:
            ax.plot(puts_data['strike'], puts_data['charm_display'], 
                   color=self.colors['negative_charm'], linewidth=3, label='Puts Charm', zorder=2)
        
        # Add critical level line
        ax.axvline(x=current_price, color=self.colors['critical_line'], 
                  linestyle='--', linewidth=2, label=f'Current Price: ${current_price:.0f}', zorder=3)
        
        # Add zero line
        ax.axhline(y=0, color='black', linestyle='-', linewidth=1, alpha=0.5, zorder=1)
        
        # Formatting
        ax.set_xlabel('Strike Price ($)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Charm (×1000)', fontsize=12, fontweight='bold')
        ax.set_title('Charm Profile: The Time Decay Battlefield', fontsize=16, fontweight='bold', pad=20)
        
        # Add subtitle
        ax.text(0.5, 0.95, f'Green = Time Helps | Red = Time Destroys | Current: ${current_price:.0f}',
                transform=ax.transAxes, ha='center', va='top', fontsize=10, 
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.7))
        
        ax.legend(loc='upper right')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"Charm Profile chart saved: {output_path}")
        return output_path

    def create_gamma_charm_interaction_chart(self, options_data: pd.DataFrame,
                                           current_price: float,
                                           output_path: str) -> str:
        """
        Create the Gamma-Charm Interaction scatter plot.

        Args:
            options_data (pd.DataFrame): Options data
            current_price (float): Current underlying price
            output_path (str): Path to save the chart

        Returns:
            str: Path to the generated chart
        """
        # Calculate Greeks for all options
        options_with_greeks = self.calculate_portfolio_greeks(options_data, current_price)

        if len(options_with_greeks) == 0:
            print("Warning: No data available for Gamma-Charm Interaction chart")
            return output_path

        # Create the chart
        fig, ax = plt.subplots(figsize=self.figsize)

        # Separate calls and puts
        calls_data = options_with_greeks[options_with_greeks['option_type'] == 'call']
        puts_data = options_with_greeks[options_with_greeks['option_type'] == 'put']

        # Create scatter plots
        if len(calls_data) > 0:
            # Size based on open interest, color based on charm
            sizes = np.sqrt(calls_data.get('Open Interest', 100)) * 10
            colors = calls_data['charm']

            scatter_calls = ax.scatter(calls_data['Strike'], calls_data['gamma'],
                                     s=sizes, c=colors, cmap='RdYlGn',
                                     marker='o', alpha=0.7, label='Calls',
                                     edgecolors='black', linewidth=0.5)

        if len(puts_data) > 0:
            # Size based on open interest, color based on charm
            sizes = np.sqrt(puts_data.get('Open Interest', 100)) * 10
            colors = puts_data['charm']

            scatter_puts = ax.scatter(puts_data['Strike'], puts_data['gamma'],
                                    s=sizes, c=colors, cmap='RdYlGn',
                                    marker='^', alpha=0.7, label='Puts',
                                    edgecolors='black', linewidth=0.5)

        # Add current price line
        ax.axvline(x=current_price, color=self.colors['critical_line'],
                  linestyle='--', linewidth=2, label=f'Current Price: ${current_price:.0f}')

        # Formatting
        ax.set_xlabel('Strike Price ($)', fontsize=12, fontweight='bold')
        ax.set_ylabel('Gamma', fontsize=12, fontweight='bold')
        ax.set_title('Gamma-Charm Interaction Analysis', fontsize=16, fontweight='bold', pad=20)

        # Add subtitle
        ax.text(0.5, 0.95, 'Bubble Size = Open Interest | Color = Charm (Green=Positive, Red=Negative)',
                transform=ax.transAxes, ha='center', va='top', fontsize=10,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='lightgray', alpha=0.7))

        ax.legend(loc='upper right')
        ax.grid(True, alpha=0.3)

        # Add colorbar
        if len(options_with_greeks) > 0:
            cbar = plt.colorbar(scatter_calls if len(calls_data) > 0 else scatter_puts, ax=ax)
            cbar.set_label('Charm', rotation=270, labelpad=15)

        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Gamma-Charm Interaction chart saved: {output_path}")
        return output_path

    def create_vanna_charm_double_trap_chart(self, options_data: pd.DataFrame,
                                           current_price: float,
                                           output_path: str) -> str:
        """
        Create the Vanna-Charm Double Trap chart.

        Args:
            options_data (pd.DataFrame): Options data
            current_price (float): Current underlying price
            output_path (str): Path to save the chart

        Returns:
            str: Path to the generated chart
        """
        # Calculate Greeks for all options
        options_with_greeks = self.calculate_portfolio_greeks(options_data, current_price)

        if len(options_with_greeks) == 0:
            print("Warning: No data available for Vanna-Charm Double Trap chart")
            return output_path

        # Aggregate by strike
        strike_aggregated = options_with_greeks.groupby('Strike').agg({
            'vanna': 'sum',
            'charm': 'sum',
            'Open Interest': 'sum'
        }).reset_index()

        # Create the chart with dual y-axes
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(self.figsize[0], self.figsize[1] + 2),
                                      gridspec_kw={'height_ratios': [1, 3]})

        # Top panel: Regime indicators
        strikes = strike_aggregated['Strike']
        regime_colors = []
        for _, row in strike_aggregated.iterrows():
            if row['vanna'] > 0 and row['charm'] > 0:
                regime_colors.append('green')  # Stable regime
            elif row['vanna'] < 0 and row['charm'] < 0:
                regime_colors.append('red')    # Explosive regime
            else:
                regime_colors.append('yellow') # Mixed regime

        ax1.scatter(strikes, [1] * len(strikes), c=regime_colors, s=100, alpha=0.7)
        ax1.set_ylabel('Regime', fontsize=10)
        ax1.set_title('Market Regime Indicators', fontsize=12, fontweight='bold')
        ax1.set_ylim(0.5, 1.5)
        ax1.set_yticks([1])
        ax1.set_yticklabels(['Regime'])
        ax1.grid(True, alpha=0.3)

        # Add regime legend
        ax1.text(0.02, 0.8, '● Green: Stable | ● Red: Explosive | ● Yellow: Mixed',
                transform=ax1.transAxes, fontsize=9,
                bbox=dict(boxstyle='round,pad=0.3', facecolor='white', alpha=0.8))

        # Bottom panel: Vanna and Charm
        ax2_twin = ax2.twinx()

        # Plot Vanna (left axis)
        line1 = ax2.plot(strikes, strike_aggregated['vanna'],
                        color='blue', linewidth=3, label='Vanna', marker='o', markersize=4)
        ax2.set_ylabel('Vanna', color='blue', fontsize=12, fontweight='bold')
        ax2.tick_params(axis='y', labelcolor='blue')

        # Plot Charm (right axis)
        line2 = ax2_twin.plot(strikes, strike_aggregated['charm'],
                             color='red', linewidth=3, label='Charm', marker='s', markersize=4)
        ax2_twin.set_ylabel('Charm', color='red', fontsize=12, fontweight='bold')
        ax2_twin.tick_params(axis='y', labelcolor='red')

        # Add current price line
        ax2.axvline(x=current_price, color=self.colors['critical_line'],
                   linestyle='--', linewidth=2, label=f'Current Price: ${current_price:.0f}')

        # Add zero lines
        ax2.axhline(y=0, color='blue', linestyle='-', linewidth=1, alpha=0.3)
        ax2_twin.axhline(y=0, color='red', linestyle='-', linewidth=1, alpha=0.3)

        # Formatting
        ax2.set_xlabel('Strike Price ($)', fontsize=12, fontweight='bold')
        ax2.set_title('Vanna-Charm Double Trap Analysis', fontsize=14, fontweight='bold', pad=10)
        ax2.grid(True, alpha=0.3)

        # Combined legend
        lines1, labels1 = ax2.get_legend_handles_labels()
        lines2, labels2 = ax2_twin.get_legend_handles_labels()
        ax2.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        plt.tight_layout()
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"Vanna-Charm Double Trap chart saved: {output_path}")
        return output_path

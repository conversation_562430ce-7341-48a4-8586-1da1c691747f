#!/usr/bin/env python3
"""
Data Management Module
=====================
Handles data loading, validation, and preprocessing for options analysis.
"""

import pandas as pd
import numpy as np
import os
import glob
import re
from datetime import datetime, timed<PERSON><PERSON>
from typing import Optional, <PERSON><PERSON>

from config import TickerConfig


class DataManager:
    """Manages data loading, validation, and preprocessing for options analysis."""

    def __init__(self, ticker: str = 'SPX'):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.data = None
        self.data_source = None
    
    def find_most_recent_optionshistory_data(self) -> Optional[str]:
        """
        Find the most recent data file from optionshistory directory.
        Expected pattern: optionshistory/{year}_{quarter}_option_chain/{ticker}_complete_{year}_{quarter}.csv
        """
        # Check multiple possible locations for optionshistory directory
        possible_dirs = ["optionshistory", "../optionhistory", "../optionshistory"]
        optionshistory_dir = None

        for dir_path in possible_dirs:
            if os.path.exists(dir_path):
                optionshistory_dir = dir_path
                break

        if optionshistory_dir is None:
            return None
        
        # Pattern to match year_quarter_option_chain directories
        pattern = re.compile(r'^(\d{4})_(q[1-4])_option_chain$')
        found_files = []
        
        # Look for directories matching the pattern
        for item in os.listdir(optionshistory_dir):
            item_path = os.path.join(optionshistory_dir, item)
            if os.path.isdir(item_path):
                match = pattern.match(item)
                if match:
                    year, quarter = match.groups()
                    # Look for the corresponding CSV file
                    csv_file = os.path.join(item_path, self.ticker_config.get_data_file_pattern(year, quarter))
                    if os.path.exists(csv_file):
                        # Convert quarter to number for sorting
                        quarter_num = int(quarter[1])  # Extract number from 'q1', 'q2', etc.
                        found_files.append((int(year), quarter_num, csv_file))
        
        if not found_files:
            return None
        
        # Sort by year and quarter (most recent first)
        found_files.sort(key=lambda x: (x[0], x[1]), reverse=True)
        return found_files[0][2]  # Return the file path
    
    def load_data(self, data_file: Optional[str] = None, auto_find: bool = False) -> pd.DataFrame:
        """
        Load options data from file or generate synthetic data.
        
        Args:
            data_file: Path to data file
            auto_find: Whether to automatically find the most recent data file
            
        Returns:
            DataFrame with options data
        """
        if auto_find and not data_file:
            data_file = self.find_most_recent_optionshistory_data()
        
        if data_file and os.path.exists(data_file):
            print(f"Loading data from: {data_file}")
            self.data = pd.read_csv(data_file)
            self.data_source = f"File: {data_file}"
        else:
            print("No data file provided or file not found. Using synthetic data.")
            self.data = self._generate_synthetic_data()
            self.data_source = "Synthetic data"
        
        # Validate and preprocess data
        self.data = self._validate_and_preprocess(self.data)
        return self.data
    
    def _generate_synthetic_data(self) -> pd.DataFrame:
        """Generate synthetic options data for testing purposes."""
        np.random.seed(42)  # For reproducible results

        # Generate 8 trading days of data
        dates = pd.date_range(start='2025-06-19', end='2025-06-26', freq='D')
        dates = [d for d in dates if d.weekday() < 5]  # Only weekdays

        # Get ticker-specific price range
        price_range = self.ticker_config.get_synthetic_price_range()
        base_price = (price_range[0] + price_range[1]) / 2
        price_volatility = (price_range[1] - price_range[0]) * 0.02  # 2% daily volatility

        prices = [base_price + np.random.normal(0, price_volatility) for _ in dates]
        
        # Generate options data
        data_rows = []

        for i, date in enumerate(dates):
            current_price = prices[i]

            # Generate strikes around current price using ticker-specific range
            strike_range = self.ticker_config.strike_range
            strike_increment = self.ticker_config.get_strike_increment()
            min_strike = int(current_price * (1 - strike_range))
            max_strike = int(current_price * (1 + strike_range))
            strikes = np.arange(min_strike, max_strike, strike_increment)
            
            # Generate expiry dates (0-30 DTE)
            expiry_dates = [date + timedelta(days=d) for d in [1, 3, 7, 14, 21, 30]]
            
            for strike in strikes:
                for expiry in expiry_dates:
                    dte = (expiry - date).days
                    if dte <= 0:
                        continue
                    
                    # Generate both calls and puts
                    for option_type in ['c', 'p']:
                        moneyness = strike / current_price

                        # Generate realistic Greeks based on moneyness and DTE
                        if option_type == 'c':
                            delta = max(0.01, min(0.99, 0.5 + (current_price - strike) / (current_price * 0.1)))
                        else:
                            delta = min(-0.01, max(-0.99, -0.5 + (current_price - strike) / (current_price * 0.1)))

                        gamma = 0.01 * np.exp(-0.5 * ((strike - current_price) / (current_price * 0.05))**2) / max(1, dte/7)
                        vega = 0.2 * gamma * current_price * np.sqrt(dte/365)
                        theta = -vega * 0.1 / max(1, dte/7)

                        # Generate open interest (higher for ATM options)
                        base_oi = 1000 * np.exp(-0.5 * ((strike - current_price) / (current_price * 0.1))**2)
                        open_interest = max(10, int(base_oi * np.random.uniform(0.5, 2.0)))

                        data_rows.append({
                            'date': date,
                            'Strike': strike,
                            'Expiry Date': expiry,
                            'Call/Put': option_type,
                            'DTE': dte,  # Add DTE column
                            'Delta': delta,
                            'Gamma': gamma,
                            'Vega': vega,
                            'Theta': theta,
                            'Open Interest': open_interest,
                            self.ticker_config.price_column: current_price
                        })
        
        return pd.DataFrame(data_rows)
    
    def _validate_and_preprocess(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate and preprocess the loaded data."""
        # Convert date columns
        df['date'] = pd.to_datetime(df['date'])
        df['Expiry Date'] = pd.to_datetime(df['Expiry Date'])

        # Calculate DTE (Days to Expiry) if not present
        if 'DTE' not in df.columns:
            df['DTE'] = (df['Expiry Date'] - df['date']).dt.days

        # Ensure required columns exist
        required_columns = ['date', 'Strike', 'Expiry Date', 'Call/Put', 'DTE', 'Delta',
                          'Gamma', 'Vega', 'Theta', 'Open Interest', self.ticker_config.price_column]

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Missing required columns: {missing_columns}")

        # Remove invalid data
        df = df.dropna(subset=required_columns)
        df = df[df['Open Interest'] > 0]
        df = df[df['Strike'] > 0]

        # Sort by date and strike
        df = df.sort_values(['date', 'Strike'])

        print(f"Loaded {len(df)} rows of data")
        return df
    
    def get_latest_data(self) -> pd.DataFrame:
        """Get the most recent date's data."""
        if self.data is None:
            raise ValueError("No data loaded. Call load_data() first.")
        
        latest_date = self.data['date'].max()
        return self.data[self.data['date'] == latest_date].copy()
    


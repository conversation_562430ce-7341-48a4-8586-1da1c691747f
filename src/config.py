#!/usr/bin/env python3
"""
Configuration Module
===================
Centralized configuration for ticker-specific settings and parameters.
"""

from typing import Dict, Any


class TickerConfig:
    """Configuration class for ticker-specific settings."""
    
    # Default configurations for different tickers
    TICKER_CONFIGS = {
        'SPX': {
            'name': 'S&P 500 Index',
            'price_column': 'spx_close',
            'file_pattern': 'spx_complete_{year}_{quarter}.csv',
            'default_strike_range': 0.2,  # ±20% of current price
            'default_dte_max': 30,
            'gamma_multiplier': 0.01,  # For GEX calculation
            'contract_multiplier': 100,  # Standard options contract size
            'risk_free_rate': 0.05,
            'implied_vol': 0.20,
            'price_format': '${:.2f}',
            'typical_price_range': (3000, 7000),
            'strike_increment': 5
        },
        'VIX': {
            'name': 'CBOE Volatility Index',
            'price_column': 'vix_close',
            'file_pattern': 'vix_complete_{year}_{quarter}.csv',
            'default_strike_range': 0.3,  # ±30% of current price (VIX is more volatile)
            'default_dte_max': 30,
            'gamma_multiplier': 0.01,
            'contract_multiplier': 100,
            'risk_free_rate': 0.05,
            'implied_vol': 0.80,  # VIX options have higher implied vol
            'price_format': '{:.2f}',
            'typical_price_range': (10, 80),
            'strike_increment': 2.5
        },
        'TLT': {
            'name': '20+ Year Treasury Bond ETF',
            'price_column': 'tlt_close',
            'file_pattern': 'tlt_complete_{year}_{quarter}.csv',
            'default_strike_range': 0.15,  # ±15% of current price (bonds less volatile)
            'default_dte_max': 30,
            'gamma_multiplier': 0.01,
            'contract_multiplier': 100,
            'risk_free_rate': 0.05,
            'implied_vol': 0.25,
            'price_format': '${:.2f}',
            'typical_price_range': (80, 150),
            'strike_increment': 1.0
        }
    }
    
    def __init__(self, ticker: str = 'SPX'):
        """
        Initialize configuration for a specific ticker.
        
        Args:
            ticker: Ticker symbol (SPX, VIX, TLT, etc.)
        """
        self.ticker = ticker.upper()
        
        if self.ticker not in self.TICKER_CONFIGS:
            # Use SPX as default for unknown tickers
            print(f"⚠️ Unknown ticker '{self.ticker}'. Using SPX configuration as default.")
            self.ticker = 'SPX'
        
        self.config = self.TICKER_CONFIGS[self.ticker].copy()
    
    def get(self, key: str, default: Any = None) -> Any:
        """Get configuration value."""
        return self.config.get(key, default)
    
    def set(self, key: str, value: Any):
        """Set configuration value."""
        self.config[key] = value
    
    @property
    def name(self) -> str:
        """Get ticker full name."""
        return self.config['name']
    
    @property
    def price_column(self) -> str:
        """Get price column name."""
        return self.config['price_column']
    
    @property
    def file_pattern(self) -> str:
        """Get file pattern for data files."""
        return self.config['file_pattern']
    
    @property
    def strike_range(self) -> float:
        """Get default strike range percentage."""
        return self.config['default_strike_range']
    
    @property
    def max_dte(self) -> int:
        """Get maximum days to expiration."""
        return self.config['default_dte_max']
    
    @property
    def contract_multiplier(self) -> int:
        """Get options contract multiplier."""
        return self.config['contract_multiplier']
    
    @property
    def risk_free_rate(self) -> float:
        """Get risk-free rate for Black-Scholes."""
        return self.config['risk_free_rate']
    
    @property
    def implied_vol(self) -> float:
        """Get default implied volatility."""
        return self.config['implied_vol']
    
    def format_price(self, price: float) -> str:
        """Format price according to ticker conventions."""
        return self.config['price_format'].format(price)
    
    def get_synthetic_price_range(self) -> tuple:
        """Get typical price range for synthetic data generation."""
        return self.config['typical_price_range']
    
    def get_strike_increment(self) -> float:
        """Get typical strike increment."""
        return self.config['strike_increment']
    
    def get_data_file_pattern(self, year: int, quarter: str) -> str:
        """Get data file name pattern."""
        return self.file_pattern.format(year=year, quarter=quarter)
    
    @classmethod
    def get_supported_tickers(cls) -> list:
        """Get list of supported tickers."""
        return list(cls.TICKER_CONFIGS.keys())
    
    def __str__(self) -> str:
        """String representation."""
        return f"TickerConfig({self.ticker}: {self.name})"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return f"TickerConfig(ticker='{self.ticker}', name='{self.name}')"


# Global configuration instance (can be overridden)
DEFAULT_TICKER = 'SPX'

def get_config(ticker: str = None) -> TickerConfig:
    """
    Get configuration for a ticker.
    
    Args:
        ticker: Ticker symbol, defaults to DEFAULT_TICKER
        
    Returns:
        TickerConfig instance
    """
    if ticker is None:
        ticker = DEFAULT_TICKER
    return TickerConfig(ticker)


def set_default_ticker(ticker: str):
    """Set the default ticker globally."""
    global DEFAULT_TICKER
    DEFAULT_TICKER = ticker.upper()


def list_supported_tickers() -> list:
    """List all supported tickers."""
    return TickerConfig.get_supported_tickers()

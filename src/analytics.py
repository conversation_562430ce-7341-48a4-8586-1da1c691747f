#!/usr/bin/env python3
"""
Analytics Module
===============
Handles options metrics calculations, Black-Scholes functions, and trend analysis.
"""

import pandas as pd
import numpy as np
from scipy.stats import norm
from typing import Dict, List, Tuple, Optional

from config import TickerConfig


def calculate_black_scholes_vanna(S: float, K: float, T: float, r: float, sigma: float, option_type: str = 'call') -> float:
    """
    Calculate vanna using Black-Scholes model.
    Vanna = d²V/dS/dσ = vega * d2 / (S * σ)

    Parameters:
    S: Current stock price
    K: Strike price
    T: Time to expiration (in years)
    r: Risk-free rate
    sigma: Volatility
    option_type: 'call' or 'put'
    """
    if T <= 0 or sigma <= 0:
        return 0.0

    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    d2 = d1 - sigma * np.sqrt(T)

    # Calculate vega first
    vega = S * norm.pdf(d1) * np.sqrt(T) / 100  # Divide by 100 for percentage points

    # Calculate vanna = vega * d2 / (S * sigma)
    vanna = vega * d2 / (S * sigma)

    return vanna


def calculate_black_scholes_charm(S: float, K: float, T: float, r: float, sigma: float, option_type: str = 'call') -> float:
    """
    Calculate charm using Black-Scholes model.
    Charm = ∂Delta/∂Time = -∂Delta/∂T

    For calls: Charm = -[phi(d1) * (2*r*T - d2*sigma*sqrt(T)) / (2*T*sigma*sqrt(T))]
    For puts: Charm = -[phi(d1) * (2*r*T - d2*sigma*sqrt(T)) / (2*T*sigma*sqrt(T))] + r*exp(-r*T)*N(-d2)

    Parameters:
    S: Current stock price
    K: Strike price
    T: Time to expiration (in years)
    r: Risk-free rate
    sigma: Volatility
    option_type: 'call' or 'put'
    """
    if T <= 0 or sigma <= 0:
        return 0.0

    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    d2 = d1 - sigma * np.sqrt(T)

    # Common charm component
    phi_d1 = norm.pdf(d1)
    charm_common = -phi_d1 * (2 * r * T - d2 * sigma * np.sqrt(T)) / (2 * T * sigma * np.sqrt(T))

    if option_type.lower() == 'c' or option_type.lower() == 'call':
        charm = charm_common
    else:  # put
        charm = charm_common + r * np.exp(-r * T) * norm.cdf(-d2)

    return charm


class OptionsAnalytics:
    """Handles options analytics calculations and metrics."""

    def __init__(self, ticker: str = 'SPX', risk_free_rate: float = None, implied_vol: float = None):
        self.ticker_config = TickerConfig(ticker)
        self.ticker = self.ticker_config.ticker
        self.risk_free_rate = risk_free_rate or self.ticker_config.risk_free_rate
        self.implied_vol = implied_vol or self.ticker_config.implied_vol
    
    def calculate_daily_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate daily aggregated metrics from options data."""
        daily_metrics = []
        
        for date in df['date'].unique():
            day_data = df[df['date'] == date].copy()

            # Filter for forward-looking timeline (0-30 DTE)
            day_data['dte'] = (day_data['Expiry Date'] - day_data['date']).dt.days
            day_data = day_data[
                (day_data['dte'] >= 0) &
                (day_data['dte'] <= 30)
            ].copy()

            if len(day_data) == 0:
                continue

            # Get current price for the day
            current_price = day_data[self.ticker_config.price_column].iloc[0]

            # Separate calls and puts
            calls = day_data[day_data['Call/Put'] == 'c']
            puts = day_data[day_data['Call/Put'] == 'p']
            
            # Calculate GEX (Gamma Exposure)
            multiplier = self.ticker_config.contract_multiplier
            gamma_mult = self.ticker_config.get('gamma_multiplier', 0.01)
            call_gex = (calls['Gamma'] * calls['Open Interest'] * multiplier * current_price * gamma_mult).sum()
            put_gex = (puts['Gamma'] * puts['Open Interest'] * multiplier * current_price * gamma_mult * -1).sum()
            total_gex = call_gex + put_gex
            
            # Calculate other metrics
            total_theta = (day_data['Theta'] * day_data['Open Interest']).sum()
            total_vega = (day_data['Vega'] * day_data['Open Interest']).sum()

            # Calculate charm (delta decay) using Black-Scholes
            total_charm = 0.0
            for _, row in day_data.iterrows():
                if row['Open Interest'] > 0 and row['dte'] > 0:
                    time_to_expiry = row['dte'] / 365.0
                    charm = calculate_black_scholes_charm(
                        S=current_price,
                        K=row['Strike'],
                        T=time_to_expiry,
                        r=self.risk_free_rate,
                        sigma=self.implied_vol,
                        option_type=row['Call/Put']
                    )
                    total_charm += charm * row['Open Interest'] * multiplier

            # Calculate vomma (vega convexity)
            vomma = total_vega * 0.1  # Simplified vomma calculation
            
            # Open interest totals
            call_oi = calls['Open Interest'].sum()
            put_oi = puts['Open Interest'].sum()
            total_oi = call_oi + put_oi
            
            # Put/Call ratio
            pc_ratio = put_oi / call_oi if call_oi > 0 else 0
            
            daily_metrics.append({
                'date': pd.to_datetime(date),
                self.ticker_config.price_column: current_price,
                'gex': total_gex,
                'theta': total_theta,
                'vega': total_vega,
                'charm': total_charm,
                'vomma': vomma,
                'call_oi': call_oi,
                'put_oi': put_oi,
                'total_oi': total_oi,
                'pc_ratio': pc_ratio
            })
        
        return pd.DataFrame(daily_metrics).sort_values('date')
    
    def calculate_vanna_exposure(self, df: pd.DataFrame, current_price: float, max_dte: int = 30) -> pd.DataFrame:
        """
        Calculate vanna exposure profile for forward-looking timeline (0-30 DTE).

        Args:
            df: Options data DataFrame
            current_price: Current underlying price
            max_dte: Maximum days to expiration (default 30 for forward-looking analysis)

        Returns:
            DataFrame with vanna exposure by strike
        """
        # Get latest data
        latest_date = df['date'].max()
        latest_data = df[df['date'] == latest_date].copy()

        # Filter for forward-looking timeline (0-30 DTE)
        latest_data['dte'] = (latest_data['Expiry Date'] - latest_data['date']).dt.days
        forward_dte_data = latest_data[
            (latest_data['dte'] >= 0) &
            (latest_data['dte'] <= max_dte)
        ].copy()

        if len(forward_dte_data) == 0:
            return pd.DataFrame()

        # Calculate time to expiration in years
        forward_dte_data['time_to_expiry'] = forward_dte_data['dte'] / 365.0

        # Calculate Black-Scholes vanna for each option
        vanna_values = []
        for _, row in forward_dte_data.iterrows():
            if row['time_to_expiry'] > 0:
                vanna = calculate_black_scholes_vanna(
                    S=current_price,
                    K=row['Strike'],
                    T=row['time_to_expiry'],
                    r=self.risk_free_rate,
                    sigma=self.implied_vol,
                    option_type=row['Call/Put']
                )
                vanna_values.append(vanna)
            else:
                vanna_values.append(0.0)
        
        forward_dte_data['vanna'] = vanna_values

        # Calculate notional exposure: Open Interest * Strike * Contract Multiplier
        multiplier = self.ticker_config.contract_multiplier
        forward_dte_data['notional'] = forward_dte_data['Open Interest'] * forward_dte_data['Strike'] * multiplier

        # Group by strike and sum exposures
        vanna_by_strike = forward_dte_data.groupby('Strike').agg({
            'vanna': 'sum',
            'notional': 'sum'
        }).reset_index()
        
        # Filter strikes within reasonable range using ticker-specific range
        strike_range = self.ticker_config.strike_range
        min_strike = current_price * (1 - strike_range)
        max_strike = current_price * (1 + strike_range)
        vanna_by_strike = vanna_by_strike[
            (vanna_by_strike['Strike'] >= min_strike) &
            (vanna_by_strike['Strike'] <= max_strike)
        ].copy()
        
        return vanna_by_strike.sort_values('Strike')

    def calculate_forward_expiry_analysis(self, df: pd.DataFrame, current_price: float, min_dte: int = 20, max_dte: int = 30) -> pd.DataFrame:
        """
        Calculate analysis for options with forward-looking expiry dates (20-30 days forward) to provide price projection perspective.

        Args:
            df: Options data DataFrame
            current_price: Current underlying price
            min_dte: Minimum days to expiration (default 20 for forward-looking analysis)
            max_dte: Maximum days to expiration (default 30 for forward-looking timeline)

        Returns:
            DataFrame with forward expiry analysis including vanna, charm, and gamma exposure
        """
        # Get latest data
        latest_date = df['date'].max()
        latest_data = df[df['date'] == latest_date].copy()

        # Calculate DTE for all options
        latest_data['dte'] = (latest_data['Expiry Date'] - latest_data['date']).dt.days

        # Filter for forward-looking timeline (20-30 days forward)
        forward_expiry_data = latest_data[
            (latest_data['dte'] >= min_dte) &
            (latest_data['dte'] <= max_dte)
        ].copy()

        if len(forward_expiry_data) == 0:
            return pd.DataFrame()

        # Calculate time to expiration in years
        forward_expiry_data['time_to_expiry'] = forward_expiry_data['dte'] / 365.0

        # Calculate Greeks for each option
        vanna_values = []
        charm_values = []

        for _, row in forward_expiry_data.iterrows():
            if row['time_to_expiry'] > 0:
                # Calculate vanna
                vanna = calculate_black_scholes_vanna(
                    S=current_price,
                    K=row['Strike'],
                    T=row['time_to_expiry'],
                    r=self.risk_free_rate,
                    sigma=self.implied_vol,
                    option_type=row['Call/Put']
                )
                vanna_values.append(vanna)

                # Calculate charm
                charm = calculate_black_scholes_charm(
                    S=current_price,
                    K=row['Strike'],
                    T=row['time_to_expiry'],
                    r=self.risk_free_rate,
                    sigma=self.implied_vol,
                    option_type=row['Call/Put']
                )
                charm_values.append(charm)
            else:
                vanna_values.append(0.0)
                charm_values.append(0.0)

        forward_expiry_data['vanna'] = vanna_values
        forward_expiry_data['charm'] = charm_values

        # Calculate notional exposure: Open Interest * Strike * Contract Multiplier
        multiplier = self.ticker_config.contract_multiplier
        forward_expiry_data['notional'] = forward_expiry_data['Open Interest'] * forward_expiry_data['Strike'] * multiplier

        # Group by expiry date and strike for better forward perspective
        forward_analysis = forward_expiry_data.groupby(['Expiry Date', 'Strike']).agg({
            'vanna': 'sum',
            'charm': 'sum',
            'Gamma': 'sum',
            'notional': 'sum',
            'Open Interest': 'sum',
            'dte': 'first'
        }).reset_index()

        # Calculate moneyness for better perspective
        forward_analysis['moneyness'] = forward_analysis['Strike'] / current_price
        forward_analysis['pct_from_current'] = ((forward_analysis['Strike'] - current_price) / current_price) * 100

        # Filter to reasonable moneyness range (0.7 to 1.5 for comprehensive analysis)
        forward_analysis = forward_analysis[
            (forward_analysis['moneyness'] >= 0.7) &
            (forward_analysis['moneyness'] <= 1.5)
        ].copy()

        return forward_analysis.sort_values(['Expiry Date', 'Strike'])

    def analyze_trends(self, metrics_df: pd.DataFrame) -> Dict[str, str]:
        """Analyze trends in the metrics data."""
        if len(metrics_df) < 2:
            return {"trend": "insufficient_data"}
        
        # Calculate recent changes (last 3 days vs previous)
        recent_data = metrics_df.tail(3)
        previous_data = metrics_df.iloc[:-3] if len(metrics_df) > 3 else metrics_df.head(1)
        
        trends = {}
        
        # GEX trend
        recent_gex = recent_data['gex'].mean()
        previous_gex = previous_data['gex'].mean()
        gex_change = (recent_gex - previous_gex) / abs(previous_gex) if previous_gex != 0 else 0
        
        if gex_change > 0.1:
            trends['gex'] = "increasing"
        elif gex_change < -0.1:
            trends['gex'] = "decreasing"
        else:
            trends['gex'] = "stable"
        
        # Volatility trend (using vega as proxy)
        recent_vega = recent_data['vega'].mean()
        previous_vega = previous_data['vega'].mean()
        vega_change = (recent_vega - previous_vega) / abs(previous_vega) if previous_vega != 0 else 0
        
        if vega_change > 0.1:
            trends['volatility'] = "increasing"
        elif vega_change < -0.1:
            trends['volatility'] = "decreasing"
        else:
            trends['volatility'] = "stable"
        
        # Put/Call ratio trend
        recent_pc = recent_data['pc_ratio'].mean()
        previous_pc = previous_data['pc_ratio'].mean()
        pc_change = (recent_pc - previous_pc) / abs(previous_pc) if previous_pc != 0 else 0
        
        if pc_change > 0.1:
            trends['sentiment'] = "bearish"
        elif pc_change < -0.1:
            trends['sentiment'] = "bullish"
        else:
            trends['sentiment'] = "neutral"
        
        return trends
    
    def identify_anomalies(self, metrics_df: pd.DataFrame) -> List[Dict]:
        """Identify potential anomalies in the options flow."""
        anomalies = []
        
        if len(metrics_df) < 5:
            return anomalies
        
        # Calculate rolling statistics
        metrics_df = metrics_df.copy()
        metrics_df['gex_rolling_mean'] = metrics_df['gex'].rolling(window=5).mean()
        metrics_df['gex_rolling_std'] = metrics_df['gex'].rolling(window=5).std()
        
        # Identify GEX anomalies (beyond 2 standard deviations)
        for _, row in metrics_df.iterrows():
            if pd.notna(row['gex_rolling_std']) and row['gex_rolling_std'] > 0:
                z_score = abs(row['gex'] - row['gex_rolling_mean']) / row['gex_rolling_std']
                if z_score > 2:
                    anomalies.append({
                        'date': row['date'],
                        'type': 'gex_anomaly',
                        'value': row['gex'],
                        'z_score': z_score,
                        'description': f"Unusual GEX level: {row['gex']:.2f}"
                    })
        
        # Identify unusual put/call ratios
        pc_mean = metrics_df['pc_ratio'].mean()
        pc_std = metrics_df['pc_ratio'].std()
        
        for _, row in metrics_df.iterrows():
            if pc_std > 0:
                pc_z_score = abs(row['pc_ratio'] - pc_mean) / pc_std
                if pc_z_score > 2:
                    anomalies.append({
                        'date': row['date'],
                        'type': 'pc_ratio_anomaly',
                        'value': row['pc_ratio'],
                        'z_score': pc_z_score,
                        'description': f"Unusual P/C ratio: {row['pc_ratio']:.2f}"
                    })
        
        return anomalies
    
    def get_market_regime(self, metrics_df: pd.DataFrame) -> str:
        """Determine current market regime based on options metrics."""
        if len(metrics_df) == 0:
            return "unknown"
        
        latest = metrics_df.iloc[-1]
        
        # High GEX typically indicates low volatility regime
        if latest['gex'] > 100:
            if latest['pc_ratio'] > 1.2:
                return "high_gex_bearish"
            else:
                return "high_gex_bullish"
        elif latest['gex'] < -50:
            return "negative_gex"
        else:
            if latest['pc_ratio'] > 1.0:
                return "neutral_bearish"
            else:
                return "neutral_bullish"

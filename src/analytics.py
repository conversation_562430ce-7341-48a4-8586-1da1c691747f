#!/usr/bin/env python3
"""
Analytics Module
===============
Handles options metrics calculations, Black-Scholes functions, and trend analysis.
"""

import pandas as pd
import numpy as np
from scipy.stats import norm
from typing import Dict, List, Tuple, Optional


def calculate_black_scholes_vanna(S: float, K: float, T: float, r: float, sigma: float, option_type: str = 'call') -> float:
    """
    Calculate vanna using Black-Scholes model.
    Vanna = d²V/dS/dσ = vega * d2 / (S * σ)
    
    Parameters:
    S: Current stock price
    K: Strike price
    T: Time to expiration (in years)
    r: Risk-free rate
    sigma: Volatility
    option_type: 'call' or 'put'
    """
    if T <= 0 or sigma <= 0:
        return 0.0
    
    d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
    d2 = d1 - sigma * np.sqrt(T)
    
    # Calculate vega first
    vega = S * norm.pdf(d1) * np.sqrt(T) / 100  # Divide by 100 for percentage points
    
    # Calculate vanna = vega * d2 / (S * sigma)
    vanna = vega * d2 / (S * sigma)
    
    return vanna


class OptionsAnalytics:
    """Handles options analytics calculations and metrics."""
    
    def __init__(self, risk_free_rate: float = 0.05, implied_vol: float = 0.20):
        self.risk_free_rate = risk_free_rate
        self.implied_vol = implied_vol
    
    def calculate_daily_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate daily aggregated metrics from options data."""
        daily_metrics = []
        
        for date in df['date'].unique():
            day_data = df[df['date'] == date].copy()
            
            # Get SPX close price for the day
            spx_close = day_data['spx_close'].iloc[0]
            
            # Separate calls and puts
            calls = day_data[day_data['Call/Put'] == 'c']
            puts = day_data[day_data['Call/Put'] == 'p']
            
            # Calculate GEX (Gamma Exposure)
            call_gex = (calls['Gamma'] * calls['Open Interest'] * 100 * spx_close * 0.01).sum()
            put_gex = (puts['Gamma'] * puts['Open Interest'] * 100 * spx_close * 0.01 * -1).sum()
            total_gex = call_gex + put_gex
            
            # Calculate other metrics
            total_theta = (day_data['Theta'] * day_data['Open Interest']).sum()
            total_vega = (day_data['Vega'] * day_data['Open Interest']).sum()
            
            # Calculate charm (delta decay)
            charm = total_theta * -1  # Simplified charm calculation
            
            # Calculate vomma (vega convexity)
            vomma = total_vega * 0.1  # Simplified vomma calculation
            
            # Open interest totals
            call_oi = calls['Open Interest'].sum()
            put_oi = puts['Open Interest'].sum()
            total_oi = call_oi + put_oi
            
            # Put/Call ratio
            pc_ratio = put_oi / call_oi if call_oi > 0 else 0
            
            daily_metrics.append({
                'date': pd.to_datetime(date),
                'spx_close': spx_close,
                'gex': total_gex,
                'theta': total_theta,
                'vega': total_vega,
                'charm': charm,
                'vomma': vomma,
                'call_oi': call_oi,
                'put_oi': put_oi,
                'total_oi': total_oi,
                'pc_ratio': pc_ratio
            })
        
        return pd.DataFrame(daily_metrics).sort_values('date')
    
    def calculate_vanna_exposure(self, df: pd.DataFrame, current_spx: float, max_dte: int = 30) -> pd.DataFrame:
        """
        Calculate vanna exposure profile for short-dated options.
        
        Args:
            df: Options data DataFrame
            current_spx: Current SPX price
            max_dte: Maximum days to expiration to include
            
        Returns:
            DataFrame with vanna exposure by strike
        """
        # Get latest data
        latest_date = df['date'].max()
        latest_data = df[df['date'] == latest_date].copy()
        
        # Filter for short-dated options (0-30 DTE)
        latest_data['dte'] = (latest_data['Expiry Date'] - latest_data['date']).dt.days
        short_dte_data = latest_data[latest_data['dte'] <= max_dte].copy()
        
        if len(short_dte_data) == 0:
            return pd.DataFrame()
        
        # Calculate time to expiration in years
        short_dte_data['time_to_expiry'] = short_dte_data['dte'] / 365.0
        
        # Calculate Black-Scholes vanna for each option
        vanna_values = []
        for _, row in short_dte_data.iterrows():
            if row['time_to_expiry'] > 0:
                vanna = calculate_black_scholes_vanna(
                    S=current_spx,
                    K=row['Strike'],
                    T=row['time_to_expiry'],
                    r=self.risk_free_rate,
                    sigma=self.implied_vol,
                    option_type=row['Call/Put']
                )
                vanna_values.append(vanna)
            else:
                vanna_values.append(0.0)
        
        short_dte_data['vanna'] = vanna_values
        
        # Calculate notional exposure: Open Interest * Strike * 100
        short_dte_data['notional'] = short_dte_data['Open Interest'] * short_dte_data['Strike'] * 100
        
        # Group by strike and sum exposures
        vanna_by_strike = short_dte_data.groupby('Strike').agg({
            'vanna': 'sum',
            'notional': 'sum'
        }).reset_index()
        
        # Filter strikes within reasonable range (±20% of current price)
        price_range = current_spx * 0.2
        vanna_by_strike = vanna_by_strike[
            (vanna_by_strike['Strike'] >= current_spx - price_range) &
            (vanna_by_strike['Strike'] <= current_spx + price_range)
        ].copy()
        
        return vanna_by_strike.sort_values('Strike')
    
    def analyze_trends(self, metrics_df: pd.DataFrame) -> Dict[str, str]:
        """Analyze trends in the metrics data."""
        if len(metrics_df) < 2:
            return {"trend": "insufficient_data"}
        
        # Calculate recent changes (last 3 days vs previous)
        recent_data = metrics_df.tail(3)
        previous_data = metrics_df.iloc[:-3] if len(metrics_df) > 3 else metrics_df.head(1)
        
        trends = {}
        
        # GEX trend
        recent_gex = recent_data['gex'].mean()
        previous_gex = previous_data['gex'].mean()
        gex_change = (recent_gex - previous_gex) / abs(previous_gex) if previous_gex != 0 else 0
        
        if gex_change > 0.1:
            trends['gex'] = "increasing"
        elif gex_change < -0.1:
            trends['gex'] = "decreasing"
        else:
            trends['gex'] = "stable"
        
        # Volatility trend (using vega as proxy)
        recent_vega = recent_data['vega'].mean()
        previous_vega = previous_data['vega'].mean()
        vega_change = (recent_vega - previous_vega) / abs(previous_vega) if previous_vega != 0 else 0
        
        if vega_change > 0.1:
            trends['volatility'] = "increasing"
        elif vega_change < -0.1:
            trends['volatility'] = "decreasing"
        else:
            trends['volatility'] = "stable"
        
        # Put/Call ratio trend
        recent_pc = recent_data['pc_ratio'].mean()
        previous_pc = previous_data['pc_ratio'].mean()
        pc_change = (recent_pc - previous_pc) / abs(previous_pc) if previous_pc != 0 else 0
        
        if pc_change > 0.1:
            trends['sentiment'] = "bearish"
        elif pc_change < -0.1:
            trends['sentiment'] = "bullish"
        else:
            trends['sentiment'] = "neutral"
        
        return trends
    
    def identify_anomalies(self, metrics_df: pd.DataFrame) -> List[Dict]:
        """Identify potential anomalies in the options flow."""
        anomalies = []
        
        if len(metrics_df) < 5:
            return anomalies
        
        # Calculate rolling statistics
        metrics_df = metrics_df.copy()
        metrics_df['gex_rolling_mean'] = metrics_df['gex'].rolling(window=5).mean()
        metrics_df['gex_rolling_std'] = metrics_df['gex'].rolling(window=5).std()
        
        # Identify GEX anomalies (beyond 2 standard deviations)
        for _, row in metrics_df.iterrows():
            if pd.notna(row['gex_rolling_std']) and row['gex_rolling_std'] > 0:
                z_score = abs(row['gex'] - row['gex_rolling_mean']) / row['gex_rolling_std']
                if z_score > 2:
                    anomalies.append({
                        'date': row['date'],
                        'type': 'gex_anomaly',
                        'value': row['gex'],
                        'z_score': z_score,
                        'description': f"Unusual GEX level: {row['gex']:.2f}"
                    })
        
        # Identify unusual put/call ratios
        pc_mean = metrics_df['pc_ratio'].mean()
        pc_std = metrics_df['pc_ratio'].std()
        
        for _, row in metrics_df.iterrows():
            if pc_std > 0:
                pc_z_score = abs(row['pc_ratio'] - pc_mean) / pc_std
                if pc_z_score > 2:
                    anomalies.append({
                        'date': row['date'],
                        'type': 'pc_ratio_anomaly',
                        'value': row['pc_ratio'],
                        'z_score': pc_z_score,
                        'description': f"Unusual P/C ratio: {row['pc_ratio']:.2f}"
                    })
        
        return anomalies
    
    def get_market_regime(self, metrics_df: pd.DataFrame) -> str:
        """Determine current market regime based on options metrics."""
        if len(metrics_df) == 0:
            return "unknown"
        
        latest = metrics_df.iloc[-1]
        
        # High GEX typically indicates low volatility regime
        if latest['gex'] > 100:
            if latest['pc_ratio'] > 1.2:
                return "high_gex_bearish"
            else:
                return "high_gex_bullish"
        elif latest['gex'] < -50:
            return "negative_gex"
        else:
            if latest['pc_ratio'] > 1.0:
                return "neutral_bearish"
            else:
                return "neutral_bullish"

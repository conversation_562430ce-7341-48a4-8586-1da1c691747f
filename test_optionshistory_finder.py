#!/usr/bin/env python3
"""
Test script to demonstrate the optionshistory data finder functionality
"""

import os
import sys

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

# Import the function from run_analysis
from run_analysis import find_most_recent_optionshistory_data

def create_test_structure():
    """Create a test optionshistory directory structure"""
    
    # Create test directories and files
    test_dirs = [
        "optionshistory/2024_q3_option_chain",
        "optionshistory/2024_q4_option_chain", 
        "optionshistory/2025_q1_option_chain",
        "optionshistory/2025_q2_option_chain"
    ]
    
    test_files = [
        "optionshistory/2024_q3_option_chain/spx_complete_2024_q3.csv",
        "optionshistory/2024_q4_option_chain/spx_complete_2024_q4.csv",
        "optionshistory/2025_q1_option_chain/spx_complete_2025_q1.csv", 
        "optionshistory/2025_q2_option_chain/spx_complete_2025_q2.csv"
    ]
    
    # Create directories
    for dir_path in test_dirs:
        os.makedirs(dir_path, exist_ok=True)
        print(f"Created directory: {dir_path}")
    
    # Create empty CSV files
    for file_path in test_files:
        with open(file_path, 'w') as f:
            f.write("date,Strike,Expiry Date,Call/Put,Delta,Gamma,Vega,Theta,Open Interest,spx_close\n")
            f.write("2025-06-26,6000,2025-07-18,c,0.5,0.01,0.2,-0.1,1000,6025\n")
        print(f"Created file: {file_path}")

def test_finder():
    """Test the finder function"""
    print("\nTesting the optionshistory finder...")
    
    most_recent = find_most_recent_optionshistory_data()
    
    if most_recent:
        print(f"✅ Found most recent file: {most_recent}")
    else:
        print("❌ No files found")

def cleanup_test_structure():
    """Clean up the test structure"""
    import shutil
    if os.path.exists("optionshistory"):
        shutil.rmtree("optionshistory")
        print("\n🧹 Cleaned up test structure")

if __name__ == "__main__":
    print("Creating test optionshistory structure...")
    create_test_structure()
    
    test_finder()
    
    # Ask user if they want to keep the test structure
    response = input("\nKeep test structure? (y/n): ").lower().strip()
    if response != 'y':
        cleanup_test_structure()
    else:
        print("Test structure kept. You can now run:")
        print("python run_analysis.py --auto-find")

#!/usr/bin/env python3
"""
Test script to verify chart scaling is working correctly for NDX.
"""

import matplotlib.pyplot as plt
import numpy as np

def test_ndx_scaling():
    """Test that NDX scaling produces reasonable chart ranges."""
    
    # NDX values
    current_price = 22679.01
    spx_base = 6181
    scale_factor = current_price / spx_base
    
    print(f"Current NDX price: {current_price}")
    print(f"SPX base: {spx_base}")
    print(f"Scale factor: {scale_factor:.4f}")
    
    # Calculate chart ranges
    flip_point = current_price + (4 * scale_factor)
    bomb_point = current_price + (19 * scale_factor)
    chart_min = current_price - (31 * scale_factor)
    chart_max = current_price + (49 * scale_factor)
    
    print(f"\nChart ranges:")
    print(f"Chart min: {chart_min:.2f}")
    print(f"Chart max: {chart_max:.2f}")
    print(f"Chart width: {chart_max - chart_min:.2f}")
    print(f"Flip point: {flip_point:.2f}")
    print(f"Bomb point: {bomb_point:.2f}")
    
    # Create test chart
    fig, ax = plt.subplots(figsize=(10, 6), dpi=100)
    
    # Generate test data
    strikes = np.linspace(chart_min, chart_max, 100)
    test_data = np.sin((strikes - current_price) / 50) * 1000
    
    # Plot
    ax.plot(strikes, test_data, 'b-', linewidth=2, label='Test Data')
    ax.axvline(x=current_price, color='red', linestyle='--', label=f'Current NDX: {current_price:.0f}')
    ax.axvline(x=flip_point, color='orange', linestyle='--', label=f'Flip Point: {flip_point:.0f}')
    ax.axvline(x=bomb_point, color='purple', linestyle='--', label=f'Bomb Point: {bomb_point:.0f}')
    
    # Set limits
    ax.set_xlim(chart_min, chart_max)
    ax.set_ylim(-1500, 1500)
    
    # Labels
    ax.set_xlabel('Strike Price')
    ax.set_ylabel('Test Value')
    ax.set_title('NDX Chart Scaling Test')
    ax.legend()
    ax.grid(True, alpha=0.3)
    
    # Save
    plt.tight_layout()
    plt.savefig('test_ndx_scaling.png', dpi=100, bbox_inches='tight')
    print(f"\nTest chart saved as: test_ndx_scaling.png")
    
    # Check if ranges are reasonable
    if chart_max - chart_min > 1000:
        print("⚠️  WARNING: Chart range is very wide!")
    else:
        print("✅ Chart range looks reasonable")
        
    plt.close()

if __name__ == "__main__":
    test_ndx_scaling()
